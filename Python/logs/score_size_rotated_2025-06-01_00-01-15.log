2025-06-01 00:01:15 | INFO     | score:setup_logging:75 - Logging configured - Log file: /home/<USER>/repos/autolodge_retrained_deploy/test/../Python/logs/score_2025-06-01_00-01-15.log
2025-06-01 00:01:15 | WARNING  | score:_get_symspell_dictionary_path:101 - Failed to use importlib.resources: importlib.resources failed. Falling back to pkg_resources.
2025-06-01 00:01:15 | INFO     | score:_load_model_from_path:462 - Loading Keras model from: /path/to/model.h5
2025-06-01 00:01:15 | INFO     | score:_load_model_from_path:464 - Keras model loaded successfully
2025-06-01 00:01:15 | INFO     | score:_load_model_from_path:462 - Loading Keras model from: /path/to/model_dir/model.h5
2025-06-01 00:01:15 | INFO     | score:_load_model_from_path:464 - Keras model loaded successfully
2025-06-01 00:01:15 | INFO     | score:_load_model_from_path:462 - Loading Keras model from: /invalid/path
2025-06-01 00:01:15 | ERROR    | score:_load_model_from_path:468 - Failed to load Keras model: Model loading failed
2025-06-01 00:01:16 | INFO     | score:_load_preprocessing_components:486 - Loading tokenizer...
2025-06-01 00:01:16 | INFO     | score:_load_preprocessing_components:491 - Loading label encoder...
2025-06-01 00:01:16 | INFO     | score:_load_preprocessing_components:496 - Loading stopwords...
2025-06-01 00:01:16 | INFO     | score:_load_preprocessing_components:500 - Loading corpus...
2025-06-01 00:01:16 | INFO     | score:_load_preprocessing_components:505 - Initializing spell checker...
2025-06-01 00:01:16 | INFO     | score:_load_preprocessing_components:510 - All preprocessing components loaded successfully
2025-06-01 00:01:16 | INFO     | score:_load_preprocessing_components:486 - Loading tokenizer...
2025-06-01 00:01:16 | ERROR    | score:_load_preprocessing_components:514 - Failed to load preprocessing components: 
2025-06-01 00:01:16 | INFO     | score:initialize:525 - Starting initialization with local files...
2025-06-01 00:01:16 | INFO     | score:initialize:528 - Downloading NLTK stopwords...
2025-06-01 00:01:16 | INFO     | score:initialize:532 - Loading model from: /home/<USER>/repos/autolodge_retrained_deploy/test/../Python/resources/autolodge.h5
2025-06-01 00:01:16 | INFO     | score:initialize:541 - Initialization completed successfully with local files
2025-06-01 00:01:16 | INFO     | score:initialize:525 - Starting initialization with local files...
2025-06-01 00:01:16 | INFO     | score:initialize:528 - Downloading NLTK stopwords...
2025-06-01 00:01:16 | INFO     | score:initialize:532 - Loading model from: /home/<USER>/repos/autolodge_retrained_deploy/test/../Python/resources/autolodge.h5
2025-06-01 00:01:16 | ERROR    | score:initialize:544 - Initialization failed: Initialization failed
2025-06-01 00:01:16 | INFO     | score:initialize_local:558 - Running in local testing mode...
2025-06-01 00:01:16 | INFO     | score:initialize_local:564 - Loading model from: /home/<USER>/repos/autolodge_retrained_deploy/test/../Python/resources/autolodge.h5
2025-06-01 00:01:16 | INFO     | score:initialize_local:587 - Local initialization completed successfully
2025-06-01 00:01:16 | INFO     | score:initialize_local:558 - Running in local testing mode...
2025-06-01 00:01:16 | INFO     | score:initialize_local:564 - Loading model from: /home/<USER>/repos/autolodge_retrained_deploy/test/../Python/resources/autolodge.h5
2025-06-01 00:01:16 | ERROR    | score:initialize_local:590 - Local initialization failed: Local initialization failed
2025-06-01 00:01:16 | INFO     | score:preprocess:636 - Starting preprocessing pipeline...
2025-06-01 00:01:16 | INFO     | score:preprocess:662 - Preprocessing completed for 2 items
2025-06-01 00:01:16 | ERROR    | score:preprocess:666 - Preprocessing failed: Resource manager not initialized. Call init() first.
2025-06-01 00:01:16 | DEBUG    | score:preprocess_legacy:699 - start 1: 00:01:16
2025-06-01 00:01:16 | DEBUG    | score:preprocess_legacy:723 - start 2: 00:01:16
2025-06-01 00:01:16 | DEBUG    | score:preprocess_legacy:743 - start 3: 00:01:16
2025-06-01 00:01:16 | DEBUG    | score:preprocess_legacy:750 - start 4: 00:01:16
2025-06-01 00:01:16 | DEBUG    | score:preprocess_legacy:758 - start 5: 00:01:16
2025-06-01 00:01:16 | DEBUG    | score:preprocess_legacy:763 - start 6: 00:01:16
2025-06-01 00:01:16 | DEBUG    | score:preprocess_legacy:817 - start 7: 00:01:16
2025-06-01 00:01:16 | DEBUG    | score:preprocess_legacy:831 - start 8: 00:01:16
2025-06-01 00:01:16 | DEBUG    | score:preprocess_legacy:837 - start 9: 00:01:16
2025-06-01 00:01:16 | DEBUG    | score:preprocess_legacy:846 - Done: 00:01:16
2025-06-01 00:01:16 | ERROR    | score:preprocess_legacy:851 - Preprocessing failed: Resource manager not initialized. Call init() first.
2025-06-01 00:01:16 | INFO     | score:run:871 - Starting inference...
2025-06-01 00:01:16 | INFO     | score:run:889 - Processing 2 items
2025-06-01 00:01:16 | INFO     | score:run:946 - Inference completed successfully for 2 items
2025-06-01 00:01:16 | INFO     | score:run:871 - Starting inference...
2025-06-01 00:01:16 | ERROR    | score:run:950 - Inference failed: Input data is empty
2025-06-01 00:01:16 | INFO     | score:run:871 - Starting inference...
2025-06-01 00:01:16 | ERROR    | score:run:950 - Inference failed: Model or label encoder not initialized. Call init() first.
2025-06-01 00:01:16 | INFO     | score:run:871 - Starting inference...
2025-06-01 00:01:16 | INFO     | score:run:889 - Processing 1 items
2025-06-01 00:01:16 | ERROR    | score:run:895 - Model prediction failed: Model prediction failed
2025-06-01 00:01:16 | ERROR    | score:run:950 - Inference failed: Model prediction failed: Model prediction failed
2025-06-01 00:01:16 | INFO     | score:run:871 - Starting inference...
2025-06-01 00:01:16 | INFO     | score:run:889 - Processing 1 items
2025-06-01 00:01:16 | INFO     | score:run:946 - Inference completed successfully for 1 items
