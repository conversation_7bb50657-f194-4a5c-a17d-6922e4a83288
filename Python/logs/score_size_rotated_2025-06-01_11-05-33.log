2025-06-01 11:05:33 | INFO     | __main__:setup_logging:75 - Logging configured - Log file: /home/<USER>/repos/autolodge_retrained_deploy/Python/logs/score_2025-06-01_11-05-33.log
2025-06-01 11:05:33 | INFO     | __main__:<module>:957 - Running in local testing mode...
2025-06-01 11:05:33 | INFO     | __main__:initialize:525 - Starting initialization with local files...
2025-06-01 11:05:33 | INFO     | __main__:initialize:528 - Downloading NLTK stopwords...
2025-06-01 11:05:33 | INFO     | __main__:initialize:532 - Loading model from: /home/<USER>/repos/autolodge_retrained_deploy/Python/resources/autolodge.h5
2025-06-01 11:05:33 | INFO     | __main__:_load_model_from_path:462 - Loading Keras model from: /home/<USER>/repos/autolodge_retrained_deploy/Python/resources/autolodge.h5
2025-06-01 11:05:34 | INFO     | __main__:_load_model_from_path:464 - Keras model loaded successfully
2025-06-01 11:05:34 | INFO     | __main__:_load_preprocessing_components:486 - Loading tokenizer...
2025-06-01 11:05:34 | INFO     | __main__:_load_preprocessing_components:491 - Loading label encoder...
2025-06-01 11:05:34 | INFO     | __main__:_load_preprocessing_components:496 - Loading stopwords...
2025-06-01 11:05:34 | INFO     | __main__:_load_preprocessing_components:500 - Loading corpus...
2025-06-01 11:05:34 | INFO     | __main__:_load_preprocessing_components:505 - Initializing spell checker...
2025-06-01 11:05:39 | INFO     | __main__:_load_preprocessing_components:510 - All preprocessing components loaded successfully
2025-06-01 11:05:39 | INFO     | __main__:initialize:541 - Initialization completed successfully with local files
2025-06-01 11:05:39 | INFO     | __main__:run:871 - Starting inference...
2025-06-01 11:05:39 | INFO     | __main__:preprocess:636 - Starting preprocessing pipeline...
2025-06-01 11:05:39 | INFO     | __main__:preprocess:662 - Preprocessing completed for 3 items
2025-06-01 11:05:39 | INFO     | __main__:run:889 - Processing 3 items
2025-06-01 11:05:39 | INFO     | __main__:run:946 - Inference completed successfully for 3 items
