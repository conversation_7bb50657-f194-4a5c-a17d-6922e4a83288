2025-05-30 11:49:30.080 | INFO     | __main__:setup_logging:98 - Logging configured - Log file: logs/deployment_2025-05-30_11-49-30.log
2025-05-30 11:49:30.081 | INFO     | __main__:deployment_context:954 - Starting deployment context
2025-05-30 11:49:30.081 | INFO     | __main__:main:974 - 🚀 AZURE ML DEPLOYMENT SCRIPT STARTED
2025-05-30 11:49:30.081 | INFO     | __main__:main:975 - ============================================================
2025-05-30 11:49:30.081 | INFO     | __main__:main:978 - 📋 STEP 1: Loading and validating configuration...
2025-05-30 11:49:30.084 | INFO     | __main__:load_and_validate_environment:942 - Environment configuration loaded and validated successfully
2025-05-30 11:49:30.084 | INFO     | __main__:main:980 - ✅ Configuration loaded successfully
2025-05-30 11:49:30.084 | INFO     | __main__:main:983 - 📊 STEP 2: Displaying deployment summary...
2025-05-30 11:49:30.084 | INFO     | __main__:display_deployment_summary:176 - ================================================================================
2025-05-30 11:49:30.084 | INFO     | __main__:display_deployment_summary:177 - DEPLOYMENT CONFIGURATION SUMMARY
2025-05-30 11:49:30.084 | INFO     | __main__:display_deployment_summary:178 - ================================================================================
2025-05-30 11:49:30.084 | INFO     | __main__:display_deployment_summary:181 - 🔧 Azure Environment:
2025-05-30 11:49:30.084 | INFO     | __main__:display_deployment_summary:182 -    Subscription ID: b15ae5d0-8f07-4cfb-aca3-508d38e9d983
2025-05-30 11:49:30.084 | INFO     | __main__:display_deployment_summary:183 -    Resource Group:  ps-preprod-rg-claimsauto
2025-05-30 11:49:30.084 | INFO     | __main__:display_deployment_summary:184 -    Workspace:       ps-preprod-ml-claimsauto
2025-05-30 11:49:30.084 | INFO     | __main__:display_deployment_summary:187 - 🚀 Deployment Configuration:
2025-05-30 11:49:30.084 | INFO     | __main__:display_deployment_summary:188 -    Model Name:      ps-preprod-ca-tstar
2025-05-30 11:49:30.084 | INFO     | __main__:display_deployment_summary:189 -    Environment:     ps-preprod-ca-tstar
2025-05-30 11:49:30.084 | INFO     | __main__:display_deployment_summary:190 -    Endpoint Name:   ps-preprod-ca-tstar
2025-05-30 11:49:30.084 | INFO     | __main__:display_deployment_summary:191 -    Deployment Name: ps-preprod-ca-tstar
2025-05-30 11:49:30.085 | INFO     | __main__:display_deployment_summary:194 - 💻 Infrastructure:
2025-05-30 11:49:30.085 | INFO     | __main__:display_deployment_summary:195 -    Instance Type:   Standard_DS3_v2
2025-05-30 11:49:30.085 | INFO     | __main__:display_deployment_summary:196 -    Instance Count:  1
2025-05-30 11:49:30.085 | INFO     | __main__:display_deployment_summary:197 -    Timeout:         1800 seconds
2025-05-30 11:49:30.085 | INFO     | __main__:display_deployment_summary:198 -    Max Retries:     1
2025-05-30 11:49:30.085 | INFO     | __main__:display_deployment_summary:201 - 📁 File Validation:
2025-05-30 11:49:30.085 | INFO     | __main__:display_deployment_summary:207 -    Model File:      ✅ EXISTS (60.4 MB)
2025-05-30 11:49:30.085 | INFO     | __main__:display_deployment_summary:208 -    Model Path:      /home/<USER>/repos/autolodge_retrained_deploy/data/models/autolodge.h5
2025-05-30 11:49:30.085 | INFO     | __main__:display_deployment_summary:213 -    Conda File:      ✅ EXISTS
2025-05-30 11:49:30.085 | INFO     | __main__:display_deployment_summary:214 -    Conda Path:      /home/<USER>/repos/autolodge_retrained_deploy/configs/autolodge.yaml
2025-05-30 11:49:30.085 | INFO     | __main__:display_deployment_summary:219 -    Scoring Script:  ✅ EXISTS
2025-05-30 11:49:30.085 | INFO     | __main__:display_deployment_summary:220 -    Script Path:     /home/<USER>/repos/autolodge_retrained_deploy/Python/score.py
2025-05-30 11:49:30.086 | INFO     | __main__:display_deployment_summary:223 - 🔐 Environment Variables:
2025-05-30 11:49:30.086 | INFO     | __main__:display_deployment_summary:237 -    AZURE_SUBSCRIPTION_ID: ✅ b15ae5d0...
2025-05-30 11:49:30.086 | INFO     | __main__:display_deployment_summary:237 -    AZURE_RESOURCE_GROUP: ✅ ps-preprod-rg-claimsauto
2025-05-30 11:49:30.086 | INFO     | __main__:display_deployment_summary:237 -    AZURE_ML_WORKSPACE_NAME: ✅ ps-preprod-ml-claimsauto
2025-05-30 11:49:30.086 | INFO     | __main__:display_deployment_summary:237 -    MODEL_NAME: ✅ ps-preprod-ca-tstar
2025-05-30 11:49:30.086 | INFO     | __main__:display_deployment_summary:237 -    ENV_NAME: ✅ ps-preprod-ca-tstar
2025-05-30 11:49:30.086 | INFO     | __main__:display_deployment_summary:237 -    ENDPOINT_NAME: ✅ ps-preprod-ca-tstar
2025-05-30 11:49:30.086 | INFO     | __main__:display_deployment_summary:237 -    DEPLOYMENT_NAME: ✅ ps-preprod-ca-tstar
2025-05-30 11:49:30.086 | INFO     | __main__:display_deployment_summary:239 - ================================================================================
2025-05-30 11:49:30.086 | INFO     | __main__:main:987 - 🔐 STEP 3: User confirmation required...
2025-05-30 11:49:30.086 | INFO     | __main__:get_user_confirmation:252 - ⚠️  DEPLOYMENT CONFIRMATION REQUIRED
2025-05-30 11:49:30.086 | INFO     | __main__:get_user_confirmation:253 - ==================================================
2025-05-30 11:49:30.086 | WARNING  | __main__:get_user_confirmation:256 - 🎯 Target Environment: ps-preprod-ml-claimsauto
2025-05-30 11:49:30.086 | WARNING  | __main__:get_user_confirmation:257 - 🚀 Deployment Name: ps-preprod-ca-tstar
2025-05-30 11:49:30.086 | WARNING  | __main__:get_user_confirmation:258 - 📦 Model: ps-preprod-ca-tstar
2025-05-30 11:49:30.087 | WARNING  | __main__:get_user_confirmation:259 - 💰 Instance Type: Standard_DS3_v2 (Count: 1)
2025-05-30 11:49:30.087 | INFO     | __main__:get_user_confirmation:262 - ⚠️  Potential Risks:
2025-05-30 11:49:30.087 | INFO     | __main__:get_user_confirmation:263 -    • This will create/update Azure ML resources
2025-05-30 11:49:30.087 | INFO     | __main__:get_user_confirmation:264 -    • Existing deployments with the same name may be overwritten
2025-05-30 11:49:30.087 | INFO     | __main__:get_user_confirmation:265 -    • Azure costs will be incurred for compute resources
2025-05-30 11:49:30.087 | INFO     | __main__:get_user_confirmation:266 -    • The deployment process may take 15-30 minutes
2025-05-30 11:49:30.087 | INFO     | __main__:get_user_confirmation:268 - ==================================================
2025-05-30 11:49:44.285 | WARNING  | __main__:get_user_confirmation:283 - Invalid response: ''. Please enter 'y' or 'n'.
2025-05-30 11:49:44.286 | INFO     | __main__:get_user_confirmation:285 - Attempt 2/3
2025-05-30 11:49:47.250 | INFO     | __main__:get_user_confirmation:277 - ✅ User confirmed deployment. Proceeding...
2025-05-30 11:49:47.251 | INFO     | __main__:main:993 - 🔧 STEP 4: Initializing Azure ML deployer...
2025-05-30 11:49:47.251 | INFO     | __main__:__init__:310 - Initialized AzureMLDeployer with configuration
2025-05-30 11:49:47.251 | INFO     | __main__:main:995 - ✅ Deployer initialized successfully
2025-05-30 11:49:47.251 | INFO     | __main__:main:998 - 🚀 STEP 5: Executing deployment...
2025-05-30 11:49:47.251 | INFO     | __main__:main:999 - ⚠️  WARNING: Irreversible deployment operations will now begin!
2025-05-30 11:49:47.251 | INFO     | __main__:deploy_with_retry:846 - 🔄 DEPLOYMENT EXECUTION PHASE
2025-05-30 11:49:47.251 | INFO     | __main__:deploy_with_retry:847 - ==================================================
2025-05-30 11:49:47.251 | INFO     | __main__:deploy_with_retry:848 - Starting deployment attempt 1/1
2025-05-30 11:49:47.251 | INFO     | __main__:deploy_with_retry:854 - 📦 STEP 1/5: Model Registration
2025-05-30 11:49:47.251 | INFO     | __main__:register_model:428 - 🔍 MODEL REGISTRATION PHASE
2025-05-30 11:49:47.251 | INFO     | __main__:register_model:429 - ========================================
2025-05-30 11:49:47.251 | INFO     | __main__:register_model:430 - Checking for existing model: ps-preprod-ca-tstar
2025-05-30 11:49:47.251 | INFO     | __main__:_create_ml_client:330 - Creating Azure ML client connection
2025-05-30 11:49:47.903 | INFO     | __main__:_create_ml_client:345 - Successfully connected to workspace: ps-preprod-ml-claimsauto
2025-05-30 11:49:48.023 | INFO     | __main__:register_model:479 - ℹ️  Model ps-preprod-ca-tstar not found, creating new registration
2025-05-30 11:49:48.023 | INFO     | __main__:register_model:483 - 📁 Validating model file: /home/<USER>/repos/autolodge_retrained_deploy/data/models/autolodge.h5
2025-05-30 11:49:48.023 | INFO     | __main__:register_model:490 - ✅ Model file validation successful:
2025-05-30 11:49:48.023 | INFO     | __main__:register_model:491 -    File size: 60.37 MB
2025-05-30 11:49:48.024 | INFO     | __main__:register_model:492 -    Full path: /home/<USER>/repos/autolodge_retrained_deploy/data/models/autolodge.h5
2025-05-30 11:49:48.024 | INFO     | __main__:register_model:502 - 🚀 Registering new model from path: /home/<USER>/repos/autolodge_retrained_deploy/data/models/autolodge.h5
2025-05-30 11:49:48.024 | INFO     | __main__:register_model:503 -    Model name: ps-preprod-ca-tstar
2025-05-30 11:49:48.024 | INFO     | __main__:register_model:504 -    Model type: custom_model
2025-05-30 11:49:53.922 | INFO     | __main__:register_model:508 - ✅ Successfully registered model:
2025-05-30 11:49:53.922 | INFO     | __main__:register_model:509 -    Name: ps-preprod-ca-tstar
2025-05-30 11:49:53.922 | INFO     | __main__:register_model:510 -    Version: 1
2025-05-30 11:49:53.922 | INFO     | __main__:register_model:511 -    ID: /subscriptions/b15ae5d0-8f07-4cfb-aca3-508d38e9d983/resourceGroups/ps-preprod-rg-claimsauto/providers/Microsoft.MachineLearningServices/workspaces/ps-preprod-ml-claimsauto/models/ps-preprod-ca-tstar/versions/1
2025-05-30 11:49:53.922 | INFO     | __main__:register_model:512 - ========================================
2025-05-30 11:49:53.922 | INFO     | __main__:deploy_with_retry:858 - 🌍 STEP 2/5: Environment Setup
2025-05-30 11:49:53.922 | INFO     | __main__:create_environment:532 - 🌍 ENVIRONMENT SETUP PHASE
2025-05-30 11:49:53.922 | INFO     | __main__:create_environment:533 - ========================================
2025-05-30 11:49:53.922 | INFO     | __main__:create_environment:534 - Checking for existing environment: ps-preprod-ca-tstar
2025-05-30 11:49:54.210 | INFO     | __main__:create_environment:546 - ℹ️  Environment ps-preprod-ca-tstar not found, creating new one
2025-05-30 11:49:54.211 | INFO     | __main__:create_environment:550 - 📁 Validating conda file: /home/<USER>/repos/autolodge_retrained_deploy/configs/autolodge.yaml
2025-05-30 11:49:54.211 | INFO     | __main__:create_environment:557 - ✅ Conda file validation successful:
2025-05-30 11:49:54.211 | INFO     | __main__:create_environment:558 -    File size: 472 bytes
2025-05-30 11:49:54.211 | INFO     | __main__:create_environment:559 -    Full path: /home/<USER>/repos/autolodge_retrained_deploy/configs/autolodge.yaml
2025-05-30 11:49:54.211 | INFO     | __main__:create_environment:565 -    Conda file preview (first 10 lines):
2025-05-30 11:49:54.211 | INFO     | __main__:create_environment:567 -      1: channels:
2025-05-30 11:49:54.211 | INFO     | __main__:create_environment:567 -      2: - Microsoft
2025-05-30 11:49:54.211 | INFO     | __main__:create_environment:567 -      3: - defaults
2025-05-30 11:49:54.211 | INFO     | __main__:create_environment:567 -      4: dependencies:
2025-05-30 11:49:54.211 | INFO     | __main__:create_environment:567 -      5: - pip
2025-05-30 11:49:54.211 | INFO     | __main__:create_environment:567 -      6: - python=3.9
2025-05-30 11:49:54.211 | INFO     | __main__:create_environment:567 -      7: - pip:
2025-05-30 11:49:54.212 | INFO     | __main__:create_environment:567 -      8: - azure-ai-ml==1.27.1
2025-05-30 11:49:54.212 | INFO     | __main__:create_environment:567 -      9: - azure-identity==1.23.0
2025-05-30 11:49:54.212 | INFO     | __main__:create_environment:567 -      10: - azureml-inference-server-http==1.4.0
2025-05-30 11:49:54.212 | INFO     | __main__:create_environment:569 -      ... (file continues)
2025-05-30 11:49:54.215 | INFO     | __main__:create_environment:582 - 🚀 Creating new environment:
2025-05-30 11:49:54.215 | INFO     | __main__:create_environment:583 -    Environment name: ps-preprod-ca-tstar
2025-05-30 11:49:54.215 | INFO     | __main__:create_environment:584 -    Base image: mcr.microsoft.com/azureml/openmpi3.1.2-ubuntu18.04
2025-05-30 11:49:54.215 | INFO     | __main__:create_environment:585 -    Conda file: /home/<USER>/repos/autolodge_retrained_deploy/configs/autolodge.yaml
2025-05-30 11:50:01.414 | INFO     | __main__:create_environment:589 - ✅ Successfully created environment:
2025-05-30 11:50:01.414 | INFO     | __main__:create_environment:590 -    Name: ps-preprod-ca-tstar
2025-05-30 11:50:01.414 | INFO     | __main__:create_environment:591 -    Version: 1
2025-05-30 11:50:01.415 | INFO     | __main__:create_environment:592 -    ID: /subscriptions/b15ae5d0-8f07-4cfb-aca3-508d38e9d983/resourceGroups/ps-preprod-rg-claimsauto/providers/Microsoft.MachineLearningServices/workspaces/ps-preprod-ml-claimsauto/environments/ps-preprod-ca-tstar/versions/1
2025-05-30 11:50:01.415 | INFO     | __main__:create_environment:593 - ========================================
2025-05-30 11:50:01.415 | INFO     | __main__:deploy_with_retry:862 - 🌐 STEP 3/5: Endpoint Creation
2025-05-30 11:50:01.415 | INFO     | __main__:create_endpoint:613 - Checking for existing endpoint: ps-preprod-ca-tstar
2025-05-30 11:50:01.482 | INFO     | __main__:create_endpoint:633 - Endpoint ps-preprod-ca-tstar not found, creating new one
2025-05-30 11:50:01.483 | INFO     | __main__:create_endpoint:643 - Creating new endpoint: ps-preprod-ca-tstar
2025-05-30 11:50:03.916 | INFO     | __main__:_wait_for_operation_completion:389 - Starting endpoint creation operation (timeout: 1800s)
2025-05-30 11:50:03.916 | INFO     | __main__:_wait_for_operation_completion:402 - endpoint creation in progress... (0s elapsed)
2025-05-30 11:50:33.940 | INFO     | __main__:_wait_for_operation_completion:402 - endpoint creation in progress... (30s elapsed)
2025-05-30 11:51:03.968 | INFO     | __main__:_wait_for_operation_completion:402 - endpoint creation in progress... (60s elapsed)
2025-05-30 11:51:08.972 | INFO     | __main__:_wait_for_operation_completion:408 - endpoint creation completed successfully in 65.1 seconds
2025-05-30 11:51:09.542 | INFO     | __main__:create_endpoint:651 - Successfully created endpoint: ps-preprod-ca-tstar
2025-05-30 11:51:09.542 | INFO     | __main__:deploy_with_retry:866 - 🚀 STEP 4/5: Deployment Creation
2025-05-30 11:51:09.542 | INFO     | __main__:create_deployment:689 - 🚀 DEPLOYMENT CREATION PHASE
2025-05-30 11:51:09.542 | INFO     | __main__:create_deployment:690 - ========================================
2025-05-30 11:51:09.542 | INFO     | __main__:create_deployment:696 - 📁 Validating deployment files:
2025-05-30 11:51:09.542 | INFO     | __main__:create_deployment:697 -    Scoring script: /home/<USER>/repos/autolodge_retrained_deploy/Python/score.py
2025-05-30 11:51:09.543 | INFO     | __main__:create_deployment:698 -    Code directory: /home/<USER>/repos/autolodge_retrained_deploy/Python
2025-05-30 11:51:09.543 | INFO     | __main__:create_deployment:705 - ✅ Scoring script validation successful:
2025-05-30 11:51:09.543 | INFO     | __main__:create_deployment:706 -    File size: 34.88 KB
2025-05-30 11:51:09.543 | INFO     | __main__:create_deployment:715 -    score.py: ✅ EXISTS
2025-05-30 11:51:09.543 | INFO     | __main__:create_deployment:720 -    resources/: ✅ EXISTS
2025-05-30 11:51:09.543 | INFO     | __main__:create_deployment:720 -    logs/: ✅ EXISTS
2025-05-30 11:51:09.543 | INFO     | __main__:create_deployment:722 - 🔧 Deployment configuration:
2025-05-30 11:51:09.543 | INFO     | __main__:create_deployment:723 -    Deployment name: ps-preprod-ca-tstar
2025-05-30 11:51:09.543 | INFO     | __main__:create_deployment:724 -    Endpoint name: ps-preprod-ca-tstar
2025-05-30 11:51:09.543 | INFO     | __main__:create_deployment:725 -    Model: ps-preprod-ca-tstar (v1)
2025-05-30 11:51:09.543 | INFO     | __main__:create_deployment:726 -    Environment: ps-preprod-ca-tstar (v1)
2025-05-30 11:51:09.543 | INFO     | __main__:create_deployment:727 -    Instance type: Standard_DS3_v2
2025-05-30 11:51:09.543 | INFO     | __main__:create_deployment:728 -    Instance count: 1
2025-05-30 11:51:09.544 | INFO     | __main__:create_deployment:741 - 🏷️  Environment variables:
2025-05-30 11:51:09.544 | INFO     | __main__:create_deployment:743 -    PYTHONPATH: /var/azureml-app/autolodge_retrained_deploy
2025-05-30 11:51:09.544 | INFO     | __main__:create_deployment:745 - 🏷️  Deployment tags:
2025-05-30 11:51:09.544 | INFO     | __main__:create_deployment:747 -    DeploymentDate: 2025-05-30T11:51:09.544058+10:00
2025-05-30 11:51:09.544 | INFO     | __main__:create_deployment:747 -    ModelName: ps-preprod-ca-tstar
2025-05-30 11:51:09.544 | INFO     | __main__:create_deployment:747 -    ModelVersion: 1
2025-05-30 11:51:09.544 | INFO     | __main__:create_deployment:747 -    EnvironmentName: ps-preprod-ca-tstar
2025-05-30 11:51:09.544 | INFO     | __main__:create_deployment:747 -    EnvironmentVersion: 1
2025-05-30 11:51:09.544 | INFO     | __main__:create_deployment:747 -    CreatedBy: AzureMLDeployer
2025-05-30 11:51:09.545 | INFO     | __main__:create_deployment:762 - 🚀 Starting deployment creation...
2025-05-30 11:51:09.545 | WARNING  | __main__:create_deployment:763 - ⏱️  This operation may take 15-30 minutes to complete
2025-05-30 11:51:15.590 | ERROR    | __main__:create_deployment:792 - ❌ Failed to create deployment ps-preprod-ca-tstar: (BadRequest) The request is invalid.
Code: BadRequest
Message: The request is invalid.
Exception Details:	(InferencingClientCallFailed) {"error":{"code":"Validation","message":"{\"errors\":{\"VmSize\":[\"Not enough quota available for Standard_DS3_v2 in SubscriptionId b15ae5d0-8f07-4cfb-aca3-508d38e9d983. Current usage/limit: 60/60. Additional needed: 8 Please see troubleshooting guide, available here: https://aka.ms/oe-tsg#error-outofquota\"]},\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"traceId\":\"00-c889068f4ed76da59c75edcdc4856667-42ba5d6df14be415-01\"}"}}
	Code: InferencingClientCallFailed
	Message: {"error":{"code":"Validation","message":"{\"errors\":{\"VmSize\":[\"Not enough quota available for Standard_DS3_v2 in SubscriptionId b15ae5d0-8f07-4cfb-aca3-508d38e9d983. Current usage/limit: 60/60. Additional needed: 8 Please see troubleshooting guide, available here: https://aka.ms/oe-tsg#error-outofquota\"]},\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"traceId\":\"00-c889068f4ed76da59c75edcdc4856667-42ba5d6df14be415-01\"}"}}
Additional Information:Type: ComponentName
Info: {
    "value": "managementfrontend"
}Type: Correlation
Info: {
    "value": {
        "operation": "c889068f4ed76da59c75edcdc4856667",
        "request": "d066ac72d7b2af39"
    }
}Type: Environment
Info: {
    "value": "australiaeast"
}Type: Location
Info: {
    "value": "australiaeast"
}Type: Time
Info: {
    "value": "2025-05-30T01:51:15.5534479+00:00"
}
2025-05-30 11:51:15.591 | ERROR    | __main__:deploy_with_retry:879 - ❌ Deployment attempt 1 failed: Failed to create deployment ps-preprod-ca-tstar: (BadRequest) The request is invalid.
Code: BadRequest
Message: The request is invalid.
Exception Details:	(InferencingClientCallFailed) {"error":{"code":"Validation","message":"{\"errors\":{\"VmSize\":[\"Not enough quota available for Standard_DS3_v2 in SubscriptionId b15ae5d0-8f07-4cfb-aca3-508d38e9d983. Current usage/limit: 60/60. Additional needed: 8 Please see troubleshooting guide, available here: https://aka.ms/oe-tsg#error-outofquota\"]},\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"traceId\":\"00-c889068f4ed76da59c75edcdc4856667-42ba5d6df14be415-01\"}"}}
	Code: InferencingClientCallFailed
	Message: {"error":{"code":"Validation","message":"{\"errors\":{\"VmSize\":[\"Not enough quota available for Standard_DS3_v2 in SubscriptionId b15ae5d0-8f07-4cfb-aca3-508d38e9d983. Current usage/limit: 60/60. Additional needed: 8 Please see troubleshooting guide, available here: https://aka.ms/oe-tsg#error-outofquota\"]},\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"traceId\":\"00-c889068f4ed76da59c75edcdc4856667-42ba5d6df14be415-01\"}"}}
Additional Information:Type: ComponentName
Info: {
    "value": "managementfrontend"
}Type: Correlation
Info: {
    "value": {
        "operation": "c889068f4ed76da59c75edcdc4856667",
        "request": "d066ac72d7b2af39"
    }
}Type: Environment
Info: {
    "value": "australiaeast"
}Type: Location
Info: {
    "value": "australiaeast"
}Type: Time
Info: {
    "value": "2025-05-30T01:51:15.5534479+00:00"
}
2025-05-30 11:51:15.591 | ERROR    | __main__:deploy_with_retry:886 - 💥 All deployment attempts failed
2025-05-30 11:51:15.591 | ERROR    | __main__:deploy_with_retry:893 - 💥 Deployment failed after 1 attempts. Last error: Failed to create deployment ps-preprod-ca-tstar: (BadRequest) The request is invalid.
Code: BadRequest
Message: The request is invalid.
Exception Details:	(InferencingClientCallFailed) {"error":{"code":"Validation","message":"{\"errors\":{\"VmSize\":[\"Not enough quota available for Standard_DS3_v2 in SubscriptionId b15ae5d0-8f07-4cfb-aca3-508d38e9d983. Current usage/limit: 60/60. Additional needed: 8 Please see troubleshooting guide, available here: https://aka.ms/oe-tsg#error-outofquota\"]},\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"traceId\":\"00-c889068f4ed76da59c75edcdc4856667-42ba5d6df14be415-01\"}"}}
	Code: InferencingClientCallFailed
	Message: {"error":{"code":"Validation","message":"{\"errors\":{\"VmSize\":[\"Not enough quota available for Standard_DS3_v2 in SubscriptionId b15ae5d0-8f07-4cfb-aca3-508d38e9d983. Current usage/limit: 60/60. Additional needed: 8 Please see troubleshooting guide, available here: https://aka.ms/oe-tsg#error-outofquota\"]},\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"traceId\":\"00-c889068f4ed76da59c75edcdc4856667-42ba5d6df14be415-01\"}"}}
Additional Information:Type: ComponentName
Info: {
    "value": "managementfrontend"
}Type: Correlation
Info: {
    "value": {
        "operation": "c889068f4ed76da59c75edcdc4856667",
        "request": "d066ac72d7b2af39"
    }
}Type: Environment
Info: {
    "value": "australiaeast"
}Type: Location
Info: {
    "value": "australiaeast"
}Type: Time
Info: {
    "value": "2025-05-30T01:51:15.5534479+00:00"
}
2025-05-30 11:51:15.591 | ERROR    | __main__:deployment_context:959 - Deployment context failed: Deployment failed after 1 attempts. Last error: Failed to create deployment ps-preprod-ca-tstar: (BadRequest) The request is invalid.
Code: BadRequest
Message: The request is invalid.
Exception Details:	(InferencingClientCallFailed) {"error":{"code":"Validation","message":"{\"errors\":{\"VmSize\":[\"Not enough quota available for Standard_DS3_v2 in SubscriptionId b15ae5d0-8f07-4cfb-aca3-508d38e9d983. Current usage/limit: 60/60. Additional needed: 8 Please see troubleshooting guide, available here: https://aka.ms/oe-tsg#error-outofquota\"]},\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"traceId\":\"00-c889068f4ed76da59c75edcdc4856667-42ba5d6df14be415-01\"}"}}
	Code: InferencingClientCallFailed
	Message: {"error":{"code":"Validation","message":"{\"errors\":{\"VmSize\":[\"Not enough quota available for Standard_DS3_v2 in SubscriptionId b15ae5d0-8f07-4cfb-aca3-508d38e9d983. Current usage/limit: 60/60. Additional needed: 8 Please see troubleshooting guide, available here: https://aka.ms/oe-tsg#error-outofquota\"]},\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"traceId\":\"00-c889068f4ed76da59c75edcdc4856667-42ba5d6df14be415-01\"}"}}
Additional Information:Type: ComponentName
Info: {
    "value": "managementfrontend"
}Type: Correlation
Info: {
    "value": {
        "operation": "c889068f4ed76da59c75edcdc4856667",
        "request": "d066ac72d7b2af39"
    }
}Type: Environment
Info: {
    "value": "australiaeast"
}Type: Location
Info: {
    "value": "australiaeast"
}Type: Time
Info: {
    "value": "2025-05-30T01:51:15.5534479+00:00"
}
2025-05-30 11:51:15.591 | INFO     | __main__:deployment_context:962 - Deployment context cleanup completed
2025-05-30 11:51:15.591 | ERROR    | __main__:main:1027 - ❌ Deployment failed: Deployment failed after 1 attempts. Last error: Failed to create deployment ps-preprod-ca-tstar: (BadRequest) The request is invalid.
Code: BadRequest
Message: The request is invalid.
Exception Details:	(InferencingClientCallFailed) {"error":{"code":"Validation","message":"{\"errors\":{\"VmSize\":[\"Not enough quota available for Standard_DS3_v2 in SubscriptionId b15ae5d0-8f07-4cfb-aca3-508d38e9d983. Current usage/limit: 60/60. Additional needed: 8 Please see troubleshooting guide, available here: https://aka.ms/oe-tsg#error-outofquota\"]},\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"traceId\":\"00-c889068f4ed76da59c75edcdc4856667-42ba5d6df14be415-01\"}"}}
	Code: InferencingClientCallFailed
	Message: {"error":{"code":"Validation","message":"{\"errors\":{\"VmSize\":[\"Not enough quota available for Standard_DS3_v2 in SubscriptionId b15ae5d0-8f07-4cfb-aca3-508d38e9d983. Current usage/limit: 60/60. Additional needed: 8 Please see troubleshooting guide, available here: https://aka.ms/oe-tsg#error-outofquota\"]},\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"traceId\":\"00-c889068f4ed76da59c75edcdc4856667-42ba5d6df14be415-01\"}"}}
Additional Information:Type: ComponentName
Info: {
    "value": "managementfrontend"
}Type: Correlation
Info: {
    "value": {
        "operation": "c889068f4ed76da59c75edcdc4856667",
        "request": "d066ac72d7b2af39"
    }
}Type: Environment
Info: {
    "value": "australiaeast"
}Type: Location
Info: {
    "value": "australiaeast"
}Type: Time
Info: {
    "value": "2025-05-30T01:51:15.5534479+00:00"
}
2025-05-30 11:51:15.591 | ERROR    | __main__:main:1028 - 💡 Check the log file for detailed error information
