2025-05-30 13:49:30.918 | INFO     | __main__:setup_logging:98 - Logging configured - Log file: logs/deployment_2025-05-30_13-49-30.log
2025-05-30 13:49:30.919 | INFO     | __main__:deployment_context:954 - Starting deployment context
2025-05-30 13:49:30.919 | INFO     | __main__:main:974 - 🚀 AZURE ML DEPLOYMENT SCRIPT STARTED
2025-05-30 13:49:30.919 | INFO     | __main__:main:975 - ============================================================
2025-05-30 13:49:30.919 | INFO     | __main__:main:978 - 📋 STEP 1: Loading and validating configuration...
2025-05-30 13:49:30.921 | INFO     | __main__:load_and_validate_environment:942 - Environment configuration loaded and validated successfully
2025-05-30 13:49:30.921 | INFO     | __main__:main:980 - ✅ Configuration loaded successfully
2025-05-30 13:49:30.921 | INFO     | __main__:main:983 - 📊 STEP 2: Displaying deployment summary...
2025-05-30 13:49:30.921 | INFO     | __main__:display_deployment_summary:176 - ================================================================================
2025-05-30 13:49:30.921 | INFO     | __main__:display_deployment_summary:177 - DEPLOYMENT CONFIGURATION SUMMARY
2025-05-30 13:49:30.921 | INFO     | __main__:display_deployment_summary:178 - ================================================================================
2025-05-30 13:49:30.921 | INFO     | __main__:display_deployment_summary:181 - 🔧 Azure Environment:
2025-05-30 13:49:30.922 | INFO     | __main__:display_deployment_summary:182 -    Subscription ID: 60181c42-6ab1-4a45-86e2-27d2a74ff8b0
2025-05-30 13:49:30.922 | INFO     | __main__:display_deployment_summary:183 -    Resource Group:  ps-prod-ml-claimsauto
2025-05-30 13:49:30.922 | INFO     | __main__:display_deployment_summary:184 -    Workspace:       ps-prod-ml-ws-claimsauto
2025-05-30 13:49:30.922 | INFO     | __main__:display_deployment_summary:187 - 🚀 Deployment Configuration:
2025-05-30 13:49:30.922 | INFO     | __main__:display_deployment_summary:188 -    Model Name:      ps-prd-ca-tstar
2025-05-30 13:49:30.922 | INFO     | __main__:display_deployment_summary:189 -    Environment:     ps-prd-ca-tstar
2025-05-30 13:49:30.922 | INFO     | __main__:display_deployment_summary:190 -    Endpoint Name:   ps-prd-ca-tstar
2025-05-30 13:49:30.922 | INFO     | __main__:display_deployment_summary:191 -    Deployment Name: ps-prd-ca-tstar
2025-05-30 13:49:30.922 | INFO     | __main__:display_deployment_summary:194 - 💻 Infrastructure:
2025-05-30 13:49:30.922 | INFO     | __main__:display_deployment_summary:195 -    Instance Type:   STANDARD_NC4AS_T4_V3
2025-05-30 13:49:30.922 | INFO     | __main__:display_deployment_summary:196 -    Instance Count:  1
2025-05-30 13:49:30.922 | INFO     | __main__:display_deployment_summary:197 -    Timeout:         1800 seconds
2025-05-30 13:49:30.922 | INFO     | __main__:display_deployment_summary:198 -    Max Retries:     1
2025-05-30 13:49:30.922 | INFO     | __main__:display_deployment_summary:201 - 📁 File Validation:
2025-05-30 13:49:30.922 | INFO     | __main__:display_deployment_summary:207 -    Model File:      ✅ EXISTS (60.4 MB)
2025-05-30 13:49:30.923 | INFO     | __main__:display_deployment_summary:208 -    Model Path:      /home/<USER>/repos/autolodge_retrained_deploy/data/models/autolodge.h5
2025-05-30 13:49:30.923 | INFO     | __main__:display_deployment_summary:213 -    Conda File:      ✅ EXISTS
2025-05-30 13:49:30.923 | INFO     | __main__:display_deployment_summary:214 -    Conda Path:      /home/<USER>/repos/autolodge_retrained_deploy/configs/autolodge.yaml
2025-05-30 13:49:30.923 | INFO     | __main__:display_deployment_summary:219 -    Scoring Script:  ✅ EXISTS
2025-05-30 13:49:30.923 | INFO     | __main__:display_deployment_summary:220 -    Script Path:     /home/<USER>/repos/autolodge_retrained_deploy/Python/score.py
2025-05-30 13:49:30.923 | INFO     | __main__:display_deployment_summary:223 - 🔐 Environment Variables:
2025-05-30 13:49:30.923 | INFO     | __main__:display_deployment_summary:237 -    AZURE_SUBSCRIPTION_ID: ✅ 60181c42...
2025-05-30 13:49:30.923 | INFO     | __main__:display_deployment_summary:237 -    AZURE_RESOURCE_GROUP: ✅ ps-prod-ml-claimsauto
2025-05-30 13:49:30.923 | INFO     | __main__:display_deployment_summary:237 -    AZURE_ML_WORKSPACE_NAME: ✅ ps-prod-ml-ws-claimsauto
2025-05-30 13:49:30.923 | INFO     | __main__:display_deployment_summary:237 -    MODEL_NAME: ✅ ps-prd-ca-tstar
2025-05-30 13:49:30.923 | INFO     | __main__:display_deployment_summary:237 -    ENV_NAME: ✅ ps-prd-ca-tstar
2025-05-30 13:49:30.923 | INFO     | __main__:display_deployment_summary:237 -    ENDPOINT_NAME: ✅ ps-prd-ca-tstar
2025-05-30 13:49:30.923 | INFO     | __main__:display_deployment_summary:237 -    DEPLOYMENT_NAME: ✅ ps-prd-ca-tstar
2025-05-30 13:49:30.924 | INFO     | __main__:display_deployment_summary:239 - ================================================================================
2025-05-30 13:49:30.924 | INFO     | __main__:main:987 - 🔐 STEP 3: User confirmation required...
2025-05-30 13:49:30.924 | INFO     | __main__:get_user_confirmation:252 - ⚠️  DEPLOYMENT CONFIRMATION REQUIRED
2025-05-30 13:49:30.924 | INFO     | __main__:get_user_confirmation:253 - ==================================================
2025-05-30 13:49:30.924 | WARNING  | __main__:get_user_confirmation:256 - 🎯 Target Environment: ps-prod-ml-ws-claimsauto
2025-05-30 13:49:30.924 | WARNING  | __main__:get_user_confirmation:257 - 🚀 Deployment Name: ps-prd-ca-tstar
2025-05-30 13:49:30.924 | WARNING  | __main__:get_user_confirmation:258 - 📦 Model: ps-prd-ca-tstar
2025-05-30 13:49:30.924 | WARNING  | __main__:get_user_confirmation:259 - 💰 Instance Type: STANDARD_NC4AS_T4_V3 (Count: 1)
2025-05-30 13:49:30.924 | INFO     | __main__:get_user_confirmation:262 - ⚠️  Potential Risks:
2025-05-30 13:49:30.924 | INFO     | __main__:get_user_confirmation:263 -    • This will create/update Azure ML resources
2025-05-30 13:49:30.924 | INFO     | __main__:get_user_confirmation:264 -    • Existing deployments with the same name may be overwritten
2025-05-30 13:49:30.924 | INFO     | __main__:get_user_confirmation:265 -    • Azure costs will be incurred for compute resources
2025-05-30 13:49:30.924 | INFO     | __main__:get_user_confirmation:266 -    • The deployment process may take 15-30 minutes
2025-05-30 13:49:30.924 | INFO     | __main__:get_user_confirmation:268 - ==================================================
2025-05-30 13:49:43.210 | INFO     | __main__:get_user_confirmation:277 - ✅ User confirmed deployment. Proceeding...
2025-05-30 13:49:43.210 | INFO     | __main__:main:993 - 🔧 STEP 4: Initializing Azure ML deployer...
2025-05-30 13:49:43.210 | INFO     | __main__:__init__:310 - Initialized AzureMLDeployer with configuration
2025-05-30 13:49:43.210 | INFO     | __main__:main:995 - ✅ Deployer initialized successfully
2025-05-30 13:49:43.210 | INFO     | __main__:main:998 - 🚀 STEP 5: Executing deployment...
2025-05-30 13:49:43.211 | INFO     | __main__:main:999 - ⚠️  WARNING: Irreversible deployment operations will now begin!
2025-05-30 13:49:43.211 | INFO     | __main__:deploy_with_retry:846 - 🔄 DEPLOYMENT EXECUTION PHASE
2025-05-30 13:49:43.211 | INFO     | __main__:deploy_with_retry:847 - ==================================================
2025-05-30 13:49:43.211 | INFO     | __main__:deploy_with_retry:848 - Starting deployment attempt 1/1
2025-05-30 13:49:43.211 | INFO     | __main__:deploy_with_retry:854 - 📦 STEP 1/5: Model Registration
2025-05-30 13:49:43.211 | INFO     | __main__:register_model:428 - 🔍 MODEL REGISTRATION PHASE
2025-05-30 13:49:43.211 | INFO     | __main__:register_model:429 - ========================================
2025-05-30 13:49:43.211 | INFO     | __main__:register_model:430 - Checking for existing model: ps-prd-ca-tstar
2025-05-30 13:49:43.211 | INFO     | __main__:_create_ml_client:330 - Creating Azure ML client connection
2025-05-30 13:49:43.847 | INFO     | __main__:_create_ml_client:345 - Successfully connected to workspace: ps-prod-ml-ws-claimsauto
2025-05-30 13:49:44.035 | INFO     | __main__:register_model:479 - ℹ️  Model ps-prd-ca-tstar not found, creating new registration
2025-05-30 13:49:44.035 | INFO     | __main__:register_model:483 - 📁 Validating model file: /home/<USER>/repos/autolodge_retrained_deploy/data/models/autolodge.h5
2025-05-30 13:49:44.035 | INFO     | __main__:register_model:490 - ✅ Model file validation successful:
2025-05-30 13:49:44.035 | INFO     | __main__:register_model:491 -    File size: 60.37 MB
2025-05-30 13:49:44.035 | INFO     | __main__:register_model:492 -    Full path: /home/<USER>/repos/autolodge_retrained_deploy/data/models/autolodge.h5
2025-05-30 13:49:44.036 | INFO     | __main__:register_model:502 - 🚀 Registering new model from path: /home/<USER>/repos/autolodge_retrained_deploy/data/models/autolodge.h5
2025-05-30 13:49:44.036 | INFO     | __main__:register_model:503 -    Model name: ps-prd-ca-tstar
2025-05-30 13:49:44.036 | INFO     | __main__:register_model:504 -    Model type: custom_model
2025-05-30 13:49:48.434 | INFO     | __main__:register_model:508 - ✅ Successfully registered model:
2025-05-30 13:49:48.434 | INFO     | __main__:register_model:509 -    Name: ps-prd-ca-tstar
2025-05-30 13:49:48.435 | INFO     | __main__:register_model:510 -    Version: 1
2025-05-30 13:49:48.435 | INFO     | __main__:register_model:511 -    ID: /subscriptions/60181c42-6ab1-4a45-86e2-27d2a74ff8b0/resourceGroups/ps-prod-ml-claimsauto/providers/Microsoft.MachineLearningServices/workspaces/ps-prod-ml-ws-claimsauto/models/ps-prd-ca-tstar/versions/1
2025-05-30 13:49:48.435 | INFO     | __main__:register_model:512 - ========================================
2025-05-30 13:49:48.435 | INFO     | __main__:deploy_with_retry:858 - 🌍 STEP 2/5: Environment Setup
2025-05-30 13:49:48.435 | INFO     | __main__:create_environment:532 - 🌍 ENVIRONMENT SETUP PHASE
2025-05-30 13:49:48.435 | INFO     | __main__:create_environment:533 - ========================================
2025-05-30 13:49:48.435 | INFO     | __main__:create_environment:534 - Checking for existing environment: ps-prd-ca-tstar
2025-05-30 13:49:48.778 | INFO     | __main__:create_environment:546 - ℹ️  Environment ps-prd-ca-tstar not found, creating new one
2025-05-30 13:49:48.778 | INFO     | __main__:create_environment:550 - 📁 Validating conda file: /home/<USER>/repos/autolodge_retrained_deploy/configs/autolodge.yaml
2025-05-30 13:49:48.778 | INFO     | __main__:create_environment:557 - ✅ Conda file validation successful:
2025-05-30 13:49:48.778 | INFO     | __main__:create_environment:558 -    File size: 472 bytes
2025-05-30 13:49:48.778 | INFO     | __main__:create_environment:559 -    Full path: /home/<USER>/repos/autolodge_retrained_deploy/configs/autolodge.yaml
2025-05-30 13:49:48.778 | INFO     | __main__:create_environment:565 -    Conda file preview (first 10 lines):
2025-05-30 13:49:48.778 | INFO     | __main__:create_environment:567 -      1: channels:
2025-05-30 13:49:48.779 | INFO     | __main__:create_environment:567 -      2: - Microsoft
2025-05-30 13:49:48.779 | INFO     | __main__:create_environment:567 -      3: - defaults
2025-05-30 13:49:48.779 | INFO     | __main__:create_environment:567 -      4: dependencies:
2025-05-30 13:49:48.779 | INFO     | __main__:create_environment:567 -      5: - pip
2025-05-30 13:49:48.779 | INFO     | __main__:create_environment:567 -      6: - python=3.9
2025-05-30 13:49:48.779 | INFO     | __main__:create_environment:567 -      7: - pip:
2025-05-30 13:49:48.779 | INFO     | __main__:create_environment:567 -      8: - azure-ai-ml==1.27.1
2025-05-30 13:49:48.779 | INFO     | __main__:create_environment:567 -      9: - azure-identity==1.23.0
2025-05-30 13:49:48.779 | INFO     | __main__:create_environment:567 -      10: - azureml-inference-server-http==1.4.0
2025-05-30 13:49:48.779 | INFO     | __main__:create_environment:569 -      ... (file continues)
2025-05-30 13:49:48.782 | INFO     | __main__:create_environment:582 - 🚀 Creating new environment:
2025-05-30 13:49:48.782 | INFO     | __main__:create_environment:583 -    Environment name: ps-prd-ca-tstar
2025-05-30 13:49:48.782 | INFO     | __main__:create_environment:584 -    Base image: mcr.microsoft.com/azureml/openmpi3.1.2-ubuntu18.04
2025-05-30 13:49:48.782 | INFO     | __main__:create_environment:585 -    Conda file: /home/<USER>/repos/autolodge_retrained_deploy/configs/autolodge.yaml
2025-05-30 13:49:55.744 | INFO     | __main__:create_environment:589 - ✅ Successfully created environment:
2025-05-30 13:49:55.744 | INFO     | __main__:create_environment:590 -    Name: ps-prd-ca-tstar
2025-05-30 13:49:55.744 | INFO     | __main__:create_environment:591 -    Version: 1
2025-05-30 13:49:55.745 | INFO     | __main__:create_environment:592 -    ID: /subscriptions/60181c42-6ab1-4a45-86e2-27d2a74ff8b0/resourceGroups/ps-prod-ml-claimsauto/providers/Microsoft.MachineLearningServices/workspaces/ps-prod-ml-ws-claimsauto/environments/ps-prd-ca-tstar/versions/1
2025-05-30 13:49:55.745 | INFO     | __main__:create_environment:593 - ========================================
2025-05-30 13:49:55.745 | INFO     | __main__:deploy_with_retry:862 - 🌐 STEP 3/5: Endpoint Creation
2025-05-30 13:49:55.745 | INFO     | __main__:create_endpoint:613 - Checking for existing endpoint: ps-prd-ca-tstar
2025-05-30 13:49:55.796 | INFO     | __main__:create_endpoint:633 - Endpoint ps-prd-ca-tstar not found, creating new one
2025-05-30 13:49:55.796 | INFO     | __main__:create_endpoint:643 - Creating new endpoint: ps-prd-ca-tstar
2025-05-30 13:49:57.792 | INFO     | __main__:_wait_for_operation_completion:389 - Starting endpoint creation operation (timeout: 1800s)
2025-05-30 13:49:57.792 | INFO     | __main__:_wait_for_operation_completion:402 - endpoint creation in progress... (0s elapsed)
2025-05-30 13:50:27.822 | INFO     | __main__:_wait_for_operation_completion:402 - endpoint creation in progress... (30s elapsed)
2025-05-30 13:50:57.847 | INFO     | __main__:_wait_for_operation_completion:402 - endpoint creation in progress... (60s elapsed)
2025-05-30 13:51:27.874 | INFO     | __main__:_wait_for_operation_completion:402 - endpoint creation in progress... (90s elapsed)
2025-05-30 13:51:32.880 | INFO     | __main__:_wait_for_operation_completion:408 - endpoint creation completed successfully in 95.1 seconds
2025-05-30 13:51:33.011 | INFO     | __main__:create_endpoint:651 - Successfully created endpoint: ps-prd-ca-tstar
2025-05-30 13:51:33.011 | INFO     | __main__:deploy_with_retry:866 - 🚀 STEP 4/5: Deployment Creation
2025-05-30 13:51:33.011 | INFO     | __main__:create_deployment:689 - 🚀 DEPLOYMENT CREATION PHASE
2025-05-30 13:51:33.011 | INFO     | __main__:create_deployment:690 - ========================================
2025-05-30 13:51:33.011 | INFO     | __main__:create_deployment:696 - 📁 Validating deployment files:
2025-05-30 13:51:33.011 | INFO     | __main__:create_deployment:697 -    Scoring script: /home/<USER>/repos/autolodge_retrained_deploy/Python/score.py
2025-05-30 13:51:33.012 | INFO     | __main__:create_deployment:698 -    Code directory: /home/<USER>/repos/autolodge_retrained_deploy/Python
2025-05-30 13:51:33.012 | INFO     | __main__:create_deployment:705 - ✅ Scoring script validation successful:
2025-05-30 13:51:33.012 | INFO     | __main__:create_deployment:706 -    File size: 34.88 KB
2025-05-30 13:51:33.012 | INFO     | __main__:create_deployment:715 -    score.py: ✅ EXISTS
2025-05-30 13:51:33.012 | INFO     | __main__:create_deployment:720 -    resources/: ✅ EXISTS
2025-05-30 13:51:33.012 | INFO     | __main__:create_deployment:720 -    logs/: ✅ EXISTS
2025-05-30 13:51:33.012 | INFO     | __main__:create_deployment:722 - 🔧 Deployment configuration:
2025-05-30 13:51:33.012 | INFO     | __main__:create_deployment:723 -    Deployment name: ps-prd-ca-tstar
2025-05-30 13:51:33.012 | INFO     | __main__:create_deployment:724 -    Endpoint name: ps-prd-ca-tstar
2025-05-30 13:51:33.012 | INFO     | __main__:create_deployment:725 -    Model: ps-prd-ca-tstar (v1)
2025-05-30 13:51:33.012 | INFO     | __main__:create_deployment:726 -    Environment: ps-prd-ca-tstar (v1)
2025-05-30 13:51:33.012 | INFO     | __main__:create_deployment:727 -    Instance type: STANDARD_NC4AS_T4_V3
2025-05-30 13:51:33.013 | INFO     | __main__:create_deployment:728 -    Instance count: 1
2025-05-30 13:51:33.013 | INFO     | __main__:create_deployment:741 - 🏷️  Environment variables:
2025-05-30 13:51:33.013 | INFO     | __main__:create_deployment:743 -    PYTHONPATH: /var/azureml-app/autolodge_retrained_deploy
2025-05-30 13:51:33.013 | INFO     | __main__:create_deployment:745 - 🏷️  Deployment tags:
2025-05-30 13:51:33.013 | INFO     | __main__:create_deployment:747 -    DeploymentDate: 2025-05-30T13:51:33.013075+10:00
2025-05-30 13:51:33.013 | INFO     | __main__:create_deployment:747 -    ModelName: ps-prd-ca-tstar
2025-05-30 13:51:33.013 | INFO     | __main__:create_deployment:747 -    ModelVersion: 1
2025-05-30 13:51:33.013 | INFO     | __main__:create_deployment:747 -    EnvironmentName: ps-prd-ca-tstar
2025-05-30 13:51:33.013 | INFO     | __main__:create_deployment:747 -    EnvironmentVersion: 1
2025-05-30 13:51:33.013 | INFO     | __main__:create_deployment:747 -    CreatedBy: AzureMLDeployer
2025-05-30 13:51:33.013 | INFO     | __main__:create_deployment:762 - 🚀 Starting deployment creation...
2025-05-30 13:51:33.014 | WARNING  | __main__:create_deployment:763 - ⏱️  This operation may take 15-30 minutes to complete
2025-05-30 13:51:40.534 | ERROR    | __main__:create_deployment:792 - ❌ Failed to create deployment ps-prd-ca-tstar: (BadRequest) The request is invalid.
Code: BadRequest
Message: The request is invalid.
Exception Details:	(InferencingClientCallFailed) {"error":{"code":"Validation","message":"{\"errors\":{\"VmSize\":[\"Not enough quota available for STANDARD_NC4AS_T4_V3 in SubscriptionId 60181c42-6ab1-4a45-86e2-27d2a74ff8b0. Current usage/limit: 0/0. Additional needed: 8 Please see troubleshooting guide, available here: https://aka.ms/oe-tsg#error-outofquota\"]},\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"traceId\":\"00-4cf90c4f5cda68a75d41fbfa3febf903-1428db603baa43a7-01\"}"}}
	Code: InferencingClientCallFailed
	Message: {"error":{"code":"Validation","message":"{\"errors\":{\"VmSize\":[\"Not enough quota available for STANDARD_NC4AS_T4_V3 in SubscriptionId 60181c42-6ab1-4a45-86e2-27d2a74ff8b0. Current usage/limit: 0/0. Additional needed: 8 Please see troubleshooting guide, available here: https://aka.ms/oe-tsg#error-outofquota\"]},\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"traceId\":\"00-4cf90c4f5cda68a75d41fbfa3febf903-1428db603baa43a7-01\"}"}}
Additional Information:Type: ComponentName
Info: {
    "value": "managementfrontend"
}Type: Correlation
Info: {
    "value": {
        "operation": "4cf90c4f5cda68a75d41fbfa3febf903",
        "request": "fd16b5b42fd4efae"
    }
}Type: Environment
Info: {
    "value": "australiaeast"
}Type: Location
Info: {
    "value": "australiaeast"
}Type: Time
Info: {
    "value": "2025-05-30T03:51:40.4676164+00:00"
}
2025-05-30 13:51:40.535 | ERROR    | __main__:deploy_with_retry:879 - ❌ Deployment attempt 1 failed: Failed to create deployment ps-prd-ca-tstar: (BadRequest) The request is invalid.
Code: BadRequest
Message: The request is invalid.
Exception Details:	(InferencingClientCallFailed) {"error":{"code":"Validation","message":"{\"errors\":{\"VmSize\":[\"Not enough quota available for STANDARD_NC4AS_T4_V3 in SubscriptionId 60181c42-6ab1-4a45-86e2-27d2a74ff8b0. Current usage/limit: 0/0. Additional needed: 8 Please see troubleshooting guide, available here: https://aka.ms/oe-tsg#error-outofquota\"]},\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"traceId\":\"00-4cf90c4f5cda68a75d41fbfa3febf903-1428db603baa43a7-01\"}"}}
	Code: InferencingClientCallFailed
	Message: {"error":{"code":"Validation","message":"{\"errors\":{\"VmSize\":[\"Not enough quota available for STANDARD_NC4AS_T4_V3 in SubscriptionId 60181c42-6ab1-4a45-86e2-27d2a74ff8b0. Current usage/limit: 0/0. Additional needed: 8 Please see troubleshooting guide, available here: https://aka.ms/oe-tsg#error-outofquota\"]},\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"traceId\":\"00-4cf90c4f5cda68a75d41fbfa3febf903-1428db603baa43a7-01\"}"}}
Additional Information:Type: ComponentName
Info: {
    "value": "managementfrontend"
}Type: Correlation
Info: {
    "value": {
        "operation": "4cf90c4f5cda68a75d41fbfa3febf903",
        "request": "fd16b5b42fd4efae"
    }
}Type: Environment
Info: {
    "value": "australiaeast"
}Type: Location
Info: {
    "value": "australiaeast"
}Type: Time
Info: {
    "value": "2025-05-30T03:51:40.4676164+00:00"
}
2025-05-30 13:51:40.535 | ERROR    | __main__:deploy_with_retry:886 - 💥 All deployment attempts failed
2025-05-30 13:51:40.535 | ERROR    | __main__:deploy_with_retry:893 - 💥 Deployment failed after 1 attempts. Last error: Failed to create deployment ps-prd-ca-tstar: (BadRequest) The request is invalid.
Code: BadRequest
Message: The request is invalid.
Exception Details:	(InferencingClientCallFailed) {"error":{"code":"Validation","message":"{\"errors\":{\"VmSize\":[\"Not enough quota available for STANDARD_NC4AS_T4_V3 in SubscriptionId 60181c42-6ab1-4a45-86e2-27d2a74ff8b0. Current usage/limit: 0/0. Additional needed: 8 Please see troubleshooting guide, available here: https://aka.ms/oe-tsg#error-outofquota\"]},\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"traceId\":\"00-4cf90c4f5cda68a75d41fbfa3febf903-1428db603baa43a7-01\"}"}}
	Code: InferencingClientCallFailed
	Message: {"error":{"code":"Validation","message":"{\"errors\":{\"VmSize\":[\"Not enough quota available for STANDARD_NC4AS_T4_V3 in SubscriptionId 60181c42-6ab1-4a45-86e2-27d2a74ff8b0. Current usage/limit: 0/0. Additional needed: 8 Please see troubleshooting guide, available here: https://aka.ms/oe-tsg#error-outofquota\"]},\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"traceId\":\"00-4cf90c4f5cda68a75d41fbfa3febf903-1428db603baa43a7-01\"}"}}
Additional Information:Type: ComponentName
Info: {
    "value": "managementfrontend"
}Type: Correlation
Info: {
    "value": {
        "operation": "4cf90c4f5cda68a75d41fbfa3febf903",
        "request": "fd16b5b42fd4efae"
    }
}Type: Environment
Info: {
    "value": "australiaeast"
}Type: Location
Info: {
    "value": "australiaeast"
}Type: Time
Info: {
    "value": "2025-05-30T03:51:40.4676164+00:00"
}
2025-05-30 13:51:40.535 | ERROR    | __main__:deployment_context:959 - Deployment context failed: Deployment failed after 1 attempts. Last error: Failed to create deployment ps-prd-ca-tstar: (BadRequest) The request is invalid.
Code: BadRequest
Message: The request is invalid.
Exception Details:	(InferencingClientCallFailed) {"error":{"code":"Validation","message":"{\"errors\":{\"VmSize\":[\"Not enough quota available for STANDARD_NC4AS_T4_V3 in SubscriptionId 60181c42-6ab1-4a45-86e2-27d2a74ff8b0. Current usage/limit: 0/0. Additional needed: 8 Please see troubleshooting guide, available here: https://aka.ms/oe-tsg#error-outofquota\"]},\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"traceId\":\"00-4cf90c4f5cda68a75d41fbfa3febf903-1428db603baa43a7-01\"}"}}
	Code: InferencingClientCallFailed
	Message: {"error":{"code":"Validation","message":"{\"errors\":{\"VmSize\":[\"Not enough quota available for STANDARD_NC4AS_T4_V3 in SubscriptionId 60181c42-6ab1-4a45-86e2-27d2a74ff8b0. Current usage/limit: 0/0. Additional needed: 8 Please see troubleshooting guide, available here: https://aka.ms/oe-tsg#error-outofquota\"]},\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"traceId\":\"00-4cf90c4f5cda68a75d41fbfa3febf903-1428db603baa43a7-01\"}"}}
Additional Information:Type: ComponentName
Info: {
    "value": "managementfrontend"
}Type: Correlation
Info: {
    "value": {
        "operation": "4cf90c4f5cda68a75d41fbfa3febf903",
        "request": "fd16b5b42fd4efae"
    }
}Type: Environment
Info: {
    "value": "australiaeast"
}Type: Location
Info: {
    "value": "australiaeast"
}Type: Time
Info: {
    "value": "2025-05-30T03:51:40.4676164+00:00"
}
2025-05-30 13:51:40.535 | INFO     | __main__:deployment_context:962 - Deployment context cleanup completed
2025-05-30 13:51:40.535 | ERROR    | __main__:main:1027 - ❌ Deployment failed: Deployment failed after 1 attempts. Last error: Failed to create deployment ps-prd-ca-tstar: (BadRequest) The request is invalid.
Code: BadRequest
Message: The request is invalid.
Exception Details:	(InferencingClientCallFailed) {"error":{"code":"Validation","message":"{\"errors\":{\"VmSize\":[\"Not enough quota available for STANDARD_NC4AS_T4_V3 in SubscriptionId 60181c42-6ab1-4a45-86e2-27d2a74ff8b0. Current usage/limit: 0/0. Additional needed: 8 Please see troubleshooting guide, available here: https://aka.ms/oe-tsg#error-outofquota\"]},\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"traceId\":\"00-4cf90c4f5cda68a75d41fbfa3febf903-1428db603baa43a7-01\"}"}}
	Code: InferencingClientCallFailed
	Message: {"error":{"code":"Validation","message":"{\"errors\":{\"VmSize\":[\"Not enough quota available for STANDARD_NC4AS_T4_V3 in SubscriptionId 60181c42-6ab1-4a45-86e2-27d2a74ff8b0. Current usage/limit: 0/0. Additional needed: 8 Please see troubleshooting guide, available here: https://aka.ms/oe-tsg#error-outofquota\"]},\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"traceId\":\"00-4cf90c4f5cda68a75d41fbfa3febf903-1428db603baa43a7-01\"}"}}
Additional Information:Type: ComponentName
Info: {
    "value": "managementfrontend"
}Type: Correlation
Info: {
    "value": {
        "operation": "4cf90c4f5cda68a75d41fbfa3febf903",
        "request": "fd16b5b42fd4efae"
    }
}Type: Environment
Info: {
    "value": "australiaeast"
}Type: Location
Info: {
    "value": "australiaeast"
}Type: Time
Info: {
    "value": "2025-05-30T03:51:40.4676164+00:00"
}
2025-05-30 13:51:40.535 | ERROR    | __main__:main:1028 - 💡 Check the log file for detailed error information
