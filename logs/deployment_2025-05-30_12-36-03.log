2025-05-30 12:36:03.753 | INFO     | __main__:setup_logging:98 - Logging configured - Log file: logs/deployment_2025-05-30_12-36-03.log
2025-05-30 12:36:03.754 | INFO     | __main__:deployment_context:954 - Starting deployment context
2025-05-30 12:36:03.754 | INFO     | __main__:main:974 - 🚀 AZURE ML DEPLOYMENT SCRIPT STARTED
2025-05-30 12:36:03.754 | INFO     | __main__:main:975 - ============================================================
2025-05-30 12:36:03.754 | INFO     | __main__:main:978 - 📋 STEP 1: Loading and validating configuration...
2025-05-30 12:36:03.756 | INFO     | __main__:load_and_validate_environment:942 - Environment configuration loaded and validated successfully
2025-05-30 12:36:03.757 | INFO     | __main__:main:980 - ✅ Configuration loaded successfully
2025-05-30 12:36:03.757 | INFO     | __main__:main:983 - 📊 STEP 2: Displaying deployment summary...
2025-05-30 12:36:03.757 | INFO     | __main__:display_deployment_summary:176 - ================================================================================
2025-05-30 12:36:03.757 | INFO     | __main__:display_deployment_summary:177 - DEPLOYMENT CONFIGURATION SUMMARY
2025-05-30 12:36:03.757 | INFO     | __main__:display_deployment_summary:178 - ================================================================================
2025-05-30 12:36:03.757 | INFO     | __main__:display_deployment_summary:181 - 🔧 Azure Environment:
2025-05-30 12:36:03.757 | INFO     | __main__:display_deployment_summary:182 -    Subscription ID: b15ae5d0-8f07-4cfb-aca3-508d38e9d983
2025-05-30 12:36:03.757 | INFO     | __main__:display_deployment_summary:183 -    Resource Group:  ps-dev-rg-datascience
2025-05-30 12:36:03.757 | INFO     | __main__:display_deployment_summary:184 -    Workspace:       ps-dev-mlw
2025-05-30 12:36:03.757 | INFO     | __main__:display_deployment_summary:187 - 🚀 Deployment Configuration:
2025-05-30 12:36:03.757 | INFO     | __main__:display_deployment_summary:188 -    Model Name:      ps-dev-ca-tstar
2025-05-30 12:36:03.757 | INFO     | __main__:display_deployment_summary:189 -    Environment:     ps-dev-ca-tstar
2025-05-30 12:36:03.757 | INFO     | __main__:display_deployment_summary:190 -    Endpoint Name:   ps-dev-ca-tstar
2025-05-30 12:36:03.757 | INFO     | __main__:display_deployment_summary:191 -    Deployment Name: ps-dev-ca-tstar
2025-05-30 12:36:03.757 | INFO     | __main__:display_deployment_summary:194 - 💻 Infrastructure:
2025-05-30 12:36:03.757 | INFO     | __main__:display_deployment_summary:195 -    Instance Type:   STANDARD_NC4AS_T4_V3
2025-05-30 12:36:03.758 | INFO     | __main__:display_deployment_summary:196 -    Instance Count:  1
2025-05-30 12:36:03.758 | INFO     | __main__:display_deployment_summary:197 -    Timeout:         1800 seconds
2025-05-30 12:36:03.758 | INFO     | __main__:display_deployment_summary:198 -    Max Retries:     1
2025-05-30 12:36:03.758 | INFO     | __main__:display_deployment_summary:201 - 📁 File Validation:
2025-05-30 12:36:03.758 | INFO     | __main__:display_deployment_summary:207 -    Model File:      ✅ EXISTS (60.4 MB)
2025-05-30 12:36:03.758 | INFO     | __main__:display_deployment_summary:208 -    Model Path:      /home/<USER>/repos/autolodge_retrained_deploy/data/models/autolodge.h5
2025-05-30 12:36:03.758 | INFO     | __main__:display_deployment_summary:213 -    Conda File:      ✅ EXISTS
2025-05-30 12:36:03.758 | INFO     | __main__:display_deployment_summary:214 -    Conda Path:      /home/<USER>/repos/autolodge_retrained_deploy/configs/autolodge.yaml
2025-05-30 12:36:03.758 | INFO     | __main__:display_deployment_summary:219 -    Scoring Script:  ✅ EXISTS
2025-05-30 12:36:03.758 | INFO     | __main__:display_deployment_summary:220 -    Script Path:     /home/<USER>/repos/autolodge_retrained_deploy/Python/score.py
2025-05-30 12:36:03.758 | INFO     | __main__:display_deployment_summary:223 - 🔐 Environment Variables:
2025-05-30 12:36:03.758 | INFO     | __main__:display_deployment_summary:237 -    AZURE_SUBSCRIPTION_ID: ✅ b15ae5d0...
2025-05-30 12:36:03.759 | INFO     | __main__:display_deployment_summary:237 -    AZURE_RESOURCE_GROUP: ✅ ps-dev-rg-datascience
2025-05-30 12:36:03.759 | INFO     | __main__:display_deployment_summary:237 -    AZURE_ML_WORKSPACE_NAME: ✅ ps-dev-mlw
2025-05-30 12:36:03.759 | INFO     | __main__:display_deployment_summary:237 -    MODEL_NAME: ✅ ps-dev-ca-tstar
2025-05-30 12:36:03.759 | INFO     | __main__:display_deployment_summary:237 -    ENV_NAME: ✅ ps-dev-ca-tstar
2025-05-30 12:36:03.759 | INFO     | __main__:display_deployment_summary:237 -    ENDPOINT_NAME: ✅ ps-dev-ca-tstar
2025-05-30 12:36:03.759 | INFO     | __main__:display_deployment_summary:237 -    DEPLOYMENT_NAME: ✅ ps-dev-ca-tstar
2025-05-30 12:36:03.759 | INFO     | __main__:display_deployment_summary:239 - ================================================================================
2025-05-30 12:36:03.759 | INFO     | __main__:main:987 - 🔐 STEP 3: User confirmation required...
2025-05-30 12:36:03.759 | INFO     | __main__:get_user_confirmation:252 - ⚠️  DEPLOYMENT CONFIRMATION REQUIRED
2025-05-30 12:36:03.759 | INFO     | __main__:get_user_confirmation:253 - ==================================================
2025-05-30 12:36:03.759 | WARNING  | __main__:get_user_confirmation:256 - 🎯 Target Environment: ps-dev-mlw
2025-05-30 12:36:03.759 | WARNING  | __main__:get_user_confirmation:257 - 🚀 Deployment Name: ps-dev-ca-tstar
2025-05-30 12:36:03.759 | WARNING  | __main__:get_user_confirmation:258 - 📦 Model: ps-dev-ca-tstar
2025-05-30 12:36:03.759 | WARNING  | __main__:get_user_confirmation:259 - 💰 Instance Type: STANDARD_NC4AS_T4_V3 (Count: 1)
2025-05-30 12:36:03.759 | INFO     | __main__:get_user_confirmation:262 - ⚠️  Potential Risks:
2025-05-30 12:36:03.760 | INFO     | __main__:get_user_confirmation:263 -    • This will create/update Azure ML resources
2025-05-30 12:36:03.760 | INFO     | __main__:get_user_confirmation:264 -    • Existing deployments with the same name may be overwritten
2025-05-30 12:36:03.760 | INFO     | __main__:get_user_confirmation:265 -    • Azure costs will be incurred for compute resources
2025-05-30 12:36:03.760 | INFO     | __main__:get_user_confirmation:266 -    • The deployment process may take 15-30 minutes
2025-05-30 12:36:03.760 | INFO     | __main__:get_user_confirmation:268 - ==================================================
2025-05-30 12:37:06.165 | WARNING  | __main__:get_user_confirmation:280 - ❌ User cancelled deployment.
2025-05-30 12:37:06.165 | WARNING  | __main__:main:989 - ❌ Deployment cancelled by user
2025-05-30 12:37:06.165 | INFO     | __main__:deployment_context:962 - Deployment context cleanup completed
