2025-05-30 13:57:14.159 | INFO     | __main__:setup_logging:98 - Logging configured - Log file: logs/deployment_2025-05-30_13-57-14.log
2025-05-30 13:57:14.160 | INFO     | __main__:deployment_context:955 - Starting deployment context
2025-05-30 13:57:14.160 | INFO     | __main__:main:975 - 🚀 AZURE ML DEPLOYMENT SCRIPT STARTED
2025-05-30 13:57:14.160 | INFO     | __main__:main:976 - ============================================================
2025-05-30 13:57:14.160 | INFO     | __main__:main:979 - 📋 STEP 1: Loading and validating configuration...
2025-05-30 13:57:14.162 | INFO     | __main__:load_and_validate_environment:943 - Environment configuration loaded and validated successfully
2025-05-30 13:57:14.162 | INFO     | __main__:main:981 - ✅ Configuration loaded successfully
2025-05-30 13:57:14.162 | INFO     | __main__:main:984 - 📊 STEP 2: Displaying deployment summary...
2025-05-30 13:57:14.163 | INFO     | __main__:display_deployment_summary:177 - ================================================================================
2025-05-30 13:57:14.163 | INFO     | __main__:display_deployment_summary:178 - DEPLOYMENT CONFIGURATION SUMMARY
2025-05-30 13:57:14.163 | INFO     | __main__:display_deployment_summary:179 - ================================================================================
2025-05-30 13:57:14.163 | INFO     | __main__:display_deployment_summary:182 - 🔧 Azure Environment:
2025-05-30 13:57:14.163 | INFO     | __main__:display_deployment_summary:183 -    Subscription ID: 60181c42-6ab1-4a45-86e2-27d2a74ff8b0
2025-05-30 13:57:14.163 | INFO     | __main__:display_deployment_summary:184 -    Resource Group:  ps-prod-ml-claimsauto
2025-05-30 13:57:14.163 | INFO     | __main__:display_deployment_summary:185 -    Workspace:       ps-prod-ml-ws-claimsauto
2025-05-30 13:57:14.163 | INFO     | __main__:display_deployment_summary:188 - 🚀 Deployment Configuration:
2025-05-30 13:57:14.163 | INFO     | __main__:display_deployment_summary:189 -    Model Name:      ps-prd-ca-tstar
2025-05-30 13:57:14.163 | INFO     | __main__:display_deployment_summary:190 -    Environment:     ps-prd-ca-tstar
2025-05-30 13:57:14.163 | INFO     | __main__:display_deployment_summary:191 -    Endpoint Name:   ps-prd-ca-tstar
2025-05-30 13:57:14.163 | INFO     | __main__:display_deployment_summary:192 -    Deployment Name: ps-prd-ca-tstar
2025-05-30 13:57:14.163 | INFO     | __main__:display_deployment_summary:195 - 💻 Infrastructure:
2025-05-30 13:57:14.163 | INFO     | __main__:display_deployment_summary:196 -    Instance Type:   STANDARD_DS3_V2
2025-05-30 13:57:14.163 | INFO     | __main__:display_deployment_summary:197 -    Instance Count:  1
2025-05-30 13:57:14.163 | INFO     | __main__:display_deployment_summary:198 -    Timeout:         1800 seconds
2025-05-30 13:57:14.164 | INFO     | __main__:display_deployment_summary:199 -    Max Retries:     1
2025-05-30 13:57:14.164 | INFO     | __main__:display_deployment_summary:202 - 📁 File Validation:
2025-05-30 13:57:14.164 | INFO     | __main__:display_deployment_summary:208 -    Model File:      ✅ EXISTS (60.4 MB)
2025-05-30 13:57:14.164 | INFO     | __main__:display_deployment_summary:209 -    Model Path:      /home/<USER>/repos/autolodge_retrained_deploy/data/models/autolodge.h5
2025-05-30 13:57:14.164 | INFO     | __main__:display_deployment_summary:214 -    Conda File:      ✅ EXISTS
2025-05-30 13:57:14.164 | INFO     | __main__:display_deployment_summary:215 -    Conda Path:      /home/<USER>/repos/autolodge_retrained_deploy/configs/autolodge.yaml
2025-05-30 13:57:14.164 | INFO     | __main__:display_deployment_summary:220 -    Scoring Script:  ✅ EXISTS
2025-05-30 13:57:14.164 | INFO     | __main__:display_deployment_summary:221 -    Script Path:     /home/<USER>/repos/autolodge_retrained_deploy/Python/score.py
2025-05-30 13:57:14.164 | INFO     | __main__:display_deployment_summary:224 - 🔐 Environment Variables:
2025-05-30 13:57:14.164 | INFO     | __main__:display_deployment_summary:238 -    AZURE_SUBSCRIPTION_ID: ✅ 60181c42...
2025-05-30 13:57:14.164 | INFO     | __main__:display_deployment_summary:238 -    AZURE_RESOURCE_GROUP: ✅ ps-prod-ml-claimsauto
2025-05-30 13:57:14.165 | INFO     | __main__:display_deployment_summary:238 -    AZURE_ML_WORKSPACE_NAME: ✅ ps-prod-ml-ws-claimsauto
2025-05-30 13:57:14.165 | INFO     | __main__:display_deployment_summary:238 -    MODEL_NAME: ✅ ps-prd-ca-tstar
2025-05-30 13:57:14.165 | INFO     | __main__:display_deployment_summary:238 -    ENV_NAME: ✅ ps-prd-ca-tstar
2025-05-30 13:57:14.165 | INFO     | __main__:display_deployment_summary:238 -    ENDPOINT_NAME: ✅ ps-prd-ca-tstar
2025-05-30 13:57:14.165 | INFO     | __main__:display_deployment_summary:238 -    DEPLOYMENT_NAME: ✅ ps-prd-ca-tstar
2025-05-30 13:57:14.165 | INFO     | __main__:display_deployment_summary:240 - ================================================================================
2025-05-30 13:57:14.165 | INFO     | __main__:main:988 - 🔐 STEP 3: User confirmation required...
2025-05-30 13:57:14.165 | INFO     | __main__:get_user_confirmation:253 - ⚠️  DEPLOYMENT CONFIRMATION REQUIRED
2025-05-30 13:57:14.165 | INFO     | __main__:get_user_confirmation:254 - ==================================================
2025-05-30 13:57:14.165 | WARNING  | __main__:get_user_confirmation:257 - 🎯 Target Environment: ps-prod-ml-ws-claimsauto
2025-05-30 13:57:14.165 | WARNING  | __main__:get_user_confirmation:258 - 🚀 Deployment Name: ps-prd-ca-tstar
2025-05-30 13:57:14.165 | WARNING  | __main__:get_user_confirmation:259 - 📦 Model: ps-prd-ca-tstar
2025-05-30 13:57:14.165 | WARNING  | __main__:get_user_confirmation:260 - 💰 Instance Type: STANDARD_DS3_V2 (Count: 1)
2025-05-30 13:57:14.165 | INFO     | __main__:get_user_confirmation:263 - ⚠️  Potential Risks:
2025-05-30 13:57:14.166 | INFO     | __main__:get_user_confirmation:264 -    • This will create/update Azure ML resources
2025-05-30 13:57:14.166 | INFO     | __main__:get_user_confirmation:265 -    • Existing deployments with the same name may be overwritten
2025-05-30 13:57:14.166 | INFO     | __main__:get_user_confirmation:266 -    • Azure costs will be incurred for compute resources
2025-05-30 13:57:14.166 | INFO     | __main__:get_user_confirmation:267 -    • The deployment process may take 15-30 minutes
2025-05-30 13:57:14.166 | INFO     | __main__:get_user_confirmation:269 - ==================================================
2025-05-30 13:57:18.559 | INFO     | __main__:get_user_confirmation:278 - ✅ User confirmed deployment. Proceeding...
2025-05-30 13:57:18.559 | INFO     | __main__:main:994 - 🔧 STEP 4: Initializing Azure ML deployer...
2025-05-30 13:57:18.559 | INFO     | __main__:__init__:311 - Initialized AzureMLDeployer with configuration
2025-05-30 13:57:18.559 | INFO     | __main__:main:996 - ✅ Deployer initialized successfully
2025-05-30 13:57:18.559 | INFO     | __main__:main:999 - 🚀 STEP 5: Executing deployment...
2025-05-30 13:57:18.559 | INFO     | __main__:main:1000 - ⚠️  WARNING: Irreversible deployment operations will now begin!
2025-05-30 13:57:18.559 | INFO     | __main__:deploy_with_retry:847 - 🔄 DEPLOYMENT EXECUTION PHASE
2025-05-30 13:57:18.559 | INFO     | __main__:deploy_with_retry:848 - ==================================================
2025-05-30 13:57:18.559 | INFO     | __main__:deploy_with_retry:849 - Starting deployment attempt 1/1
2025-05-30 13:57:18.559 | INFO     | __main__:deploy_with_retry:855 - 📦 STEP 1/5: Model Registration
2025-05-30 13:57:18.559 | INFO     | __main__:register_model:429 - 🔍 MODEL REGISTRATION PHASE
2025-05-30 13:57:18.560 | INFO     | __main__:register_model:430 - ========================================
2025-05-30 13:57:18.560 | INFO     | __main__:register_model:431 - Checking for existing model: ps-prd-ca-tstar
2025-05-30 13:57:18.560 | INFO     | __main__:_create_ml_client:331 - Creating Azure ML client connection
2025-05-30 13:57:19.289 | INFO     | __main__:_create_ml_client:346 - Successfully connected to workspace: ps-prod-ml-ws-claimsauto
2025-05-30 13:57:19.605 | INFO     | __main__:register_model:436 - ✅ Found existing model: ps-prd-ca-tstar (version: 1)
2025-05-30 13:57:19.605 | INFO     | __main__:register_model:437 -    Model ID: /subscriptions/60181c42-6ab1-4a45-86e2-27d2a74ff8b0/resourceGroups/ps-prod-ml-claimsauto/providers/Microsoft.MachineLearningServices/workspaces/ps-prod-ml-ws-claimsauto/models/ps-prd-ca-tstar/versions/1
2025-05-30 13:57:19.606 | INFO     | __main__:register_model:472 -    Created: 2025-05-30 13:49:47 AEST
2025-05-30 13:57:19.606 | INFO     | __main__:register_model:476 -    Description: Model registered on 2025-05-30T13:49:44.035821+10:00
2025-05-30 13:57:19.606 | INFO     | __main__:deploy_with_retry:859 - 🌍 STEP 2/5: Environment Setup
2025-05-30 13:57:19.606 | INFO     | __main__:create_environment:533 - 🌍 ENVIRONMENT SETUP PHASE
2025-05-30 13:57:19.606 | INFO     | __main__:create_environment:534 - ========================================
2025-05-30 13:57:19.606 | INFO     | __main__:create_environment:535 - Checking for existing environment: ps-prd-ca-tstar
2025-05-30 13:57:20.197 | INFO     | __main__:create_environment:540 - ✅ Found existing environment: ps-prd-ca-tstar (version: 1)
2025-05-30 13:57:20.197 | INFO     | __main__:create_environment:541 -    Environment ID: /subscriptions/60181c42-6ab1-4a45-86e2-27d2a74ff8b0/resourceGroups/ps-prod-ml-claimsauto/providers/Microsoft.MachineLearningServices/workspaces/ps-prod-ml-ws-claimsauto/environments/ps-prd-ca-tstar/versions/1
2025-05-30 13:57:20.198 | INFO     | __main__:create_environment:542 -    Base Image: mcr.microsoft.com/azureml/openmpi3.1.2-ubuntu18.04
2025-05-30 13:57:20.198 | INFO     | __main__:create_environment:543 -    Description: Environment created on 2025-05-30T13:49:48.779641+10:00
2025-05-30 13:57:20.198 | INFO     | __main__:deploy_with_retry:863 - 🌐 STEP 3/5: Endpoint Creation
2025-05-30 13:57:20.198 | INFO     | __main__:create_endpoint:614 - Checking for existing endpoint: ps-prd-ca-tstar
2025-05-30 13:57:20.636 | INFO     | __main__:create_endpoint:619 - Found existing endpoint: ps-prd-ca-tstar
2025-05-30 13:57:20.636 | INFO     | __main__:deploy_with_retry:867 - 🚀 STEP 4/5: Deployment Creation
2025-05-30 13:57:20.637 | INFO     | __main__:create_deployment:690 - 🚀 DEPLOYMENT CREATION PHASE
2025-05-30 13:57:20.637 | INFO     | __main__:create_deployment:691 - ========================================
2025-05-30 13:57:20.637 | INFO     | __main__:create_deployment:697 - 📁 Validating deployment files:
2025-05-30 13:57:20.637 | INFO     | __main__:create_deployment:698 -    Scoring script: /home/<USER>/repos/autolodge_retrained_deploy/Python/score.py
2025-05-30 13:57:20.637 | INFO     | __main__:create_deployment:699 -    Code directory: /home/<USER>/repos/autolodge_retrained_deploy/Python
2025-05-30 13:57:20.637 | INFO     | __main__:create_deployment:706 - ✅ Scoring script validation successful:
2025-05-30 13:57:20.637 | INFO     | __main__:create_deployment:707 -    File size: 34.88 KB
2025-05-30 13:57:20.637 | INFO     | __main__:create_deployment:716 -    score.py: ✅ EXISTS
2025-05-30 13:57:20.637 | INFO     | __main__:create_deployment:721 -    resources/: ✅ EXISTS
2025-05-30 13:57:20.637 | INFO     | __main__:create_deployment:721 -    logs/: ✅ EXISTS
2025-05-30 13:57:20.637 | INFO     | __main__:create_deployment:723 - 🔧 Deployment configuration:
2025-05-30 13:57:20.637 | INFO     | __main__:create_deployment:724 -    Deployment name: ps-prd-ca-tstar
2025-05-30 13:57:20.638 | INFO     | __main__:create_deployment:725 -    Endpoint name: ps-prd-ca-tstar
2025-05-30 13:57:20.638 | INFO     | __main__:create_deployment:726 -    Model: ps-prd-ca-tstar (v1)
2025-05-30 13:57:20.638 | INFO     | __main__:create_deployment:727 -    Environment: ps-prd-ca-tstar (v1)
2025-05-30 13:57:20.638 | INFO     | __main__:create_deployment:728 -    Instance type: STANDARD_DS3_V2
2025-05-30 13:57:20.638 | INFO     | __main__:create_deployment:729 -    Instance count: 1
2025-05-30 13:57:20.638 | INFO     | __main__:create_deployment:742 - 🏷️  Environment variables:
2025-05-30 13:57:20.638 | INFO     | __main__:create_deployment:744 -    PYTHONPATH: /var/azureml-app/autolodge_retrained_deploy
2025-05-30 13:57:20.638 | INFO     | __main__:create_deployment:746 - 🏷️  Deployment tags:
2025-05-30 13:57:20.638 | INFO     | __main__:create_deployment:748 -    DeploymentDate: 2025-05-30T13:57:20.638355+10:00
2025-05-30 13:57:20.638 | INFO     | __main__:create_deployment:748 -    ModelName: ps-prd-ca-tstar
2025-05-30 13:57:20.638 | INFO     | __main__:create_deployment:748 -    ModelVersion: 1
2025-05-30 13:57:20.638 | INFO     | __main__:create_deployment:748 -    EnvironmentName: ps-prd-ca-tstar
2025-05-30 13:57:20.638 | INFO     | __main__:create_deployment:748 -    EnvironmentVersion: 1
2025-05-30 13:57:20.638 | INFO     | __main__:create_deployment:748 -    CreatedBy: AzureMLDeployer
2025-05-30 13:57:20.639 | INFO     | __main__:create_deployment:763 - 🚀 Starting deployment creation...
2025-05-30 13:57:20.639 | WARNING  | __main__:create_deployment:764 - ⏱️  This operation may take 15-30 minutes to complete
2025-05-30 13:57:25.156 | INFO     | __main__:_wait_for_operation_completion:390 - Starting deployment creation operation (timeout: 1800s)
2025-05-30 13:57:25.156 | INFO     | __main__:_wait_for_operation_completion:403 - deployment creation in progress... (0s elapsed)
2025-05-30 13:57:55.182 | INFO     | __main__:_wait_for_operation_completion:403 - deployment creation in progress... (30s elapsed)
2025-05-30 13:58:25.212 | INFO     | __main__:_wait_for_operation_completion:403 - deployment creation in progress... (60s elapsed)
2025-05-30 13:58:55.234 | INFO     | __main__:_wait_for_operation_completion:403 - deployment creation in progress... (90s elapsed)
2025-05-30 13:59:25.264 | INFO     | __main__:_wait_for_operation_completion:403 - deployment creation in progress... (120s elapsed)
2025-05-30 13:59:55.292 | INFO     | __main__:_wait_for_operation_completion:403 - deployment creation in progress... (150s elapsed)
2025-05-30 14:00:25.321 | INFO     | __main__:_wait_for_operation_completion:403 - deployment creation in progress... (180s elapsed)
2025-05-30 14:00:55.340 | INFO     | __main__:_wait_for_operation_completion:403 - deployment creation in progress... (210s elapsed)
2025-05-30 14:01:25.367 | INFO     | __main__:_wait_for_operation_completion:403 - deployment creation in progress... (240s elapsed)
2025-05-30 14:01:55.387 | INFO     | __main__:_wait_for_operation_completion:403 - deployment creation in progress... (270s elapsed)
2025-05-30 14:02:25.410 | INFO     | __main__:_wait_for_operation_completion:403 - deployment creation in progress... (300s elapsed)
2025-05-30 14:02:55.440 | INFO     | __main__:_wait_for_operation_completion:403 - deployment creation in progress... (330s elapsed)
2025-05-30 14:03:25.470 | INFO     | __main__:_wait_for_operation_completion:403 - deployment creation in progress... (360s elapsed)
2025-05-30 14:03:55.491 | INFO     | __main__:_wait_for_operation_completion:403 - deployment creation in progress... (390s elapsed)
2025-05-30 14:04:15.505 | INFO     | __main__:_wait_for_operation_completion:409 - deployment creation completed successfully in 410.3 seconds
2025-05-30 14:04:15.685 | INFO     | __main__:create_deployment:777 - ✅ Successfully created deployment:
2025-05-30 14:04:15.686 | INFO     | __main__:create_deployment:778 -    Name: ps-prd-ca-tstar
2025-05-30 14:04:15.686 | INFO     | __main__:create_deployment:779 -    Endpoint: ps-prd-ca-tstar
2025-05-30 14:04:15.686 | INFO     | __main__:create_deployment:780 -    Status: Succeeded
2025-05-30 14:04:15.686 | INFO     | __main__:create_deployment:781 - ========================================
2025-05-30 14:04:15.686 | INFO     | __main__:deploy_with_retry:871 - 🚦 STEP 5/5: Traffic Configuration
2025-05-30 14:04:15.686 | INFO     | __main__:configure_traffic:810 - Configuring traffic for deployment: ps-prd-ca-tstar
2025-05-30 14:04:17.009 | INFO     | __main__:_wait_for_operation_completion:390 - Starting traffic configuration operation (timeout: 1800s)
2025-05-30 14:04:17.009 | INFO     | __main__:_wait_for_operation_completion:403 - traffic configuration in progress... (0s elapsed)
2025-05-30 14:04:47.038 | INFO     | __main__:_wait_for_operation_completion:403 - traffic configuration in progress... (30s elapsed)
2025-05-30 14:04:52.042 | INFO     | __main__:_wait_for_operation_completion:409 - traffic configuration completed successfully in 35.0 seconds
2025-05-30 14:04:52.273 | INFO     | __main__:configure_traffic:825 - Traffic configuration completed: {'ps-prd-ca-tstar': 100}
2025-05-30 14:04:52.273 | INFO     | __main__:deploy_with_retry:874 - ✅ All deployment steps completed successfully!
2025-05-30 14:04:52.273 | INFO     | __main__:deploy_with_retry:875 - ==================================================
2025-05-30 14:04:52.273 | INFO     | __main__:main:1004 - 🎉 DEPLOYMENT COMPLETED SUCCESSFULLY!
2025-05-30 14:04:52.273 | INFO     | __main__:main:1005 - ============================================================
2025-05-30 14:04:52.274 | INFO     | __main__:main:1006 - FINAL DEPLOYMENT SUMMARY
2025-05-30 14:04:52.274 | INFO     | __main__:main:1007 - ============================================================
2025-05-30 14:04:52.274 | INFO     | __main__:main:1008 - ✅ Endpoint: ps-prd-ca-tstar
2025-05-30 14:04:52.274 | INFO     | __main__:main:1009 - ✅ Deployment: ps-prd-ca-tstar
2025-05-30 14:04:52.274 | INFO     | __main__:main:1010 - ✅ Traffic Configuration: {'ps-prd-ca-tstar': 100}
2025-05-30 14:04:52.274 | INFO     | __main__:main:1011 - ✅ Instance Type: STANDARD_DS3_V2
2025-05-30 14:04:52.274 | INFO     | __main__:main:1012 - ✅ Instance Count: 1
2025-05-30 14:04:52.274 | INFO     | __main__:main:1013 - ✅ Workspace: ps-prod-ml-ws-claimsauto
2025-05-30 14:04:52.274 | INFO     | __main__:main:1014 - ✅ Resource Group: ps-prod-ml-claimsauto
2025-05-30 14:04:52.274 | INFO     | __main__:main:1015 - ============================================================
2025-05-30 14:04:52.274 | INFO     | __main__:main:1018 - 📋 NEXT STEPS:
2025-05-30 14:04:52.274 | INFO     | __main__:main:1019 -    1. Test the deployed endpoint with sample data
2025-05-30 14:04:52.274 | INFO     | __main__:main:1020 -    2. Monitor deployment performance and logs
2025-05-30 14:04:52.274 | INFO     | __main__:main:1021 -    3. Update traffic allocation if needed
2025-05-30 14:04:52.275 | INFO     | __main__:main:1022 -    4. Set up monitoring and alerting
2025-05-30 14:04:52.275 | INFO     | __main__:main:1023 - ============================================================
2025-05-30 14:04:52.275 | SUCCESS  | __main__:main:1025 - 🎉 Deployment completed successfully!
2025-05-30 14:04:52.275 | INFO     | __main__:deployment_context:958 - Deployment context completed successfully
2025-05-30 14:04:52.275 | INFO     | __main__:deployment_context:963 - Deployment context cleanup completed
