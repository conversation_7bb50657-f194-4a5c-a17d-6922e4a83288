2025-05-30 09:47:12.167 | INFO     | __main__:setup_logging:98 - Logging configured - Log file: logs/deployment_2025-05-30_09-47-12.log
2025-05-30 09:47:12.168 | INFO     | __main__:deployment_context:917 - Starting deployment context
2025-05-30 09:47:12.168 | INFO     | __main__:main:937 - 🚀 AZURE ML DEPLOYMENT SCRIPT STARTED
2025-05-30 09:47:12.168 | INFO     | __main__:main:938 - ============================================================
2025-05-30 09:47:12.168 | INFO     | __main__:main:941 - 📋 STEP 1: Loading and validating configuration...
2025-05-30 09:47:12.170 | INFO     | __main__:load_and_validate_environment:905 - Environment configuration loaded and validated successfully
2025-05-30 09:47:12.170 | INFO     | __main__:main:943 - ✅ Configuration loaded successfully
2025-05-30 09:47:12.170 | INFO     | __main__:main:946 - 📊 STEP 2: Displaying deployment summary...
2025-05-30 09:47:12.170 | INFO     | __main__:display_deployment_summary:176 - ================================================================================
2025-05-30 09:47:12.170 | INFO     | __main__:display_deployment_summary:177 - DEPLOYMENT CONFIGURATION SUMMARY
2025-05-30 09:47:12.171 | INFO     | __main__:display_deployment_summary:178 - ================================================================================
2025-05-30 09:47:12.171 | INFO     | __main__:display_deployment_summary:181 - 🔧 Azure Environment:
2025-05-30 09:47:12.171 | INFO     | __main__:display_deployment_summary:182 -    Subscription ID: b15ae5d0-8f07-4cfb-aca3-508d38e9d983
2025-05-30 09:47:12.171 | INFO     | __main__:display_deployment_summary:183 -    Resource Group:  ps-uat-rg-claimsautoml
2025-05-30 09:47:12.171 | INFO     | __main__:display_deployment_summary:184 -    Workspace:       ps-uat-mlw-claimsautoml
2025-05-30 09:47:12.171 | INFO     | __main__:display_deployment_summary:187 - 🚀 Deployment Configuration:
2025-05-30 09:47:12.171 | INFO     | __main__:display_deployment_summary:188 -    Model Name:      ps-dev-ca-tstarc
2025-05-30 09:47:12.171 | INFO     | __main__:display_deployment_summary:189 -    Environment:     ps-dev-ca-tstarc
2025-05-30 09:47:12.171 | INFO     | __main__:display_deployment_summary:190 -    Endpoint Name:   ps-dev-ca-tstarc
2025-05-30 09:47:12.171 | INFO     | __main__:display_deployment_summary:191 -    Deployment Name: ps-dev-ca-tstarc
2025-05-30 09:47:12.171 | INFO     | __main__:display_deployment_summary:194 - 💻 Infrastructure:
2025-05-30 09:47:12.171 | INFO     | __main__:display_deployment_summary:195 -    Instance Type:   Standard_DS3_v2
2025-05-30 09:47:12.171 | INFO     | __main__:display_deployment_summary:196 -    Instance Count:  1
2025-05-30 09:47:12.171 | INFO     | __main__:display_deployment_summary:197 -    Timeout:         1800 seconds
2025-05-30 09:47:12.171 | INFO     | __main__:display_deployment_summary:198 -    Max Retries:     1
2025-05-30 09:47:12.171 | INFO     | __main__:display_deployment_summary:201 - 📁 File Validation:
2025-05-30 09:47:12.172 | INFO     | __main__:display_deployment_summary:207 -    Model File:      ❌ MISSING 
2025-05-30 09:47:12.172 | INFO     | __main__:display_deployment_summary:208 -    Model Path:      /home/<USER>/repos/autolodge_retrained_deploy/resources/models/ps-dev-claimsauto-tstarc.h5
2025-05-30 09:47:12.172 | INFO     | __main__:display_deployment_summary:213 -    Conda File:      ✅ EXISTS
2025-05-30 09:47:12.172 | INFO     | __main__:display_deployment_summary:214 -    Conda Path:      /home/<USER>/repos/autolodge_retrained_deploy/configs/ps-dev-claimsauto-tstarc.yaml
2025-05-30 09:47:12.172 | INFO     | __main__:display_deployment_summary:219 -    Scoring Script:  ✅ EXISTS
2025-05-30 09:47:12.172 | INFO     | __main__:display_deployment_summary:220 -    Script Path:     /home/<USER>/repos/autolodge_retrained_deploy/Python/score.py
2025-05-30 09:47:12.172 | INFO     | __main__:display_deployment_summary:223 - 🔐 Environment Variables:
2025-05-30 09:47:12.172 | INFO     | __main__:display_deployment_summary:237 -    AZURE_SUBSCRIPTION_ID: ✅ b15ae5d0...
2025-05-30 09:47:12.172 | INFO     | __main__:display_deployment_summary:237 -    AZURE_RESOURCE_GROUP: ✅ ps-uat-rg-claimsautoml
2025-05-30 09:47:12.172 | INFO     | __main__:display_deployment_summary:237 -    AZURE_ML_WORKSPACE_NAME: ✅ ps-uat-mlw-claimsautoml
2025-05-30 09:47:12.172 | INFO     | __main__:display_deployment_summary:237 -    MODEL_NAME: ✅ ps-dev-ca-tstarc
2025-05-30 09:47:12.173 | INFO     | __main__:display_deployment_summary:237 -    ENV_NAME: ✅ ps-dev-ca-tstarc
2025-05-30 09:47:12.173 | INFO     | __main__:display_deployment_summary:237 -    ENDPOINT_NAME: ✅ ps-dev-ca-tstarc
2025-05-30 09:47:12.173 | INFO     | __main__:display_deployment_summary:237 -    DEPLOYMENT_NAME: ✅ ps-dev-ca-tstarc
2025-05-30 09:47:12.173 | INFO     | __main__:display_deployment_summary:239 - ================================================================================
2025-05-30 09:47:12.173 | INFO     | __main__:main:950 - 🔐 STEP 3: User confirmation required...
2025-05-30 09:47:12.173 | INFO     | __main__:get_user_confirmation:252 - ⚠️  DEPLOYMENT CONFIRMATION REQUIRED
2025-05-30 09:47:12.173 | INFO     | __main__:get_user_confirmation:253 - ==================================================
2025-05-30 09:47:12.173 | WARNING  | __main__:get_user_confirmation:256 - 🎯 Target Environment: ps-uat-mlw-claimsautoml
2025-05-30 09:47:12.173 | WARNING  | __main__:get_user_confirmation:257 - 🚀 Deployment Name: ps-dev-ca-tstarc
2025-05-30 09:47:12.173 | WARNING  | __main__:get_user_confirmation:258 - 📦 Model: ps-dev-ca-tstarc
2025-05-30 09:47:12.173 | WARNING  | __main__:get_user_confirmation:259 - 💰 Instance Type: Standard_DS3_v2 (Count: 1)
2025-05-30 09:47:12.173 | INFO     | __main__:get_user_confirmation:262 - ⚠️  Potential Risks:
2025-05-30 09:47:12.173 | INFO     | __main__:get_user_confirmation:263 -    • This will create/update Azure ML resources
2025-05-30 09:47:12.173 | INFO     | __main__:get_user_confirmation:264 -    • Existing deployments with the same name may be overwritten
2025-05-30 09:47:12.173 | INFO     | __main__:get_user_confirmation:265 -    • Azure costs will be incurred for compute resources
2025-05-30 09:47:12.174 | INFO     | __main__:get_user_confirmation:266 -    • The deployment process may take 15-30 minutes
2025-05-30 09:47:12.174 | INFO     | __main__:get_user_confirmation:268 - ==================================================
2025-05-30 09:48:37.002 | INFO     | __main__:get_user_confirmation:277 - ✅ User confirmed deployment. Proceeding...
2025-05-30 09:48:37.003 | INFO     | __main__:main:956 - 🔧 STEP 4: Initializing Azure ML deployer...
2025-05-30 09:48:37.003 | INFO     | __main__:__init__:310 - Initialized AzureMLDeployer with configuration
2025-05-30 09:48:37.003 | INFO     | __main__:main:958 - ✅ Deployer initialized successfully
2025-05-30 09:48:37.003 | INFO     | __main__:main:961 - 🚀 STEP 5: Executing deployment...
2025-05-30 09:48:37.003 | INFO     | __main__:main:962 - ⚠️  WARNING: Irreversible deployment operations will now begin!
2025-05-30 09:48:37.003 | INFO     | __main__:deploy_with_retry:809 - 🔄 DEPLOYMENT EXECUTION PHASE
2025-05-30 09:48:37.003 | INFO     | __main__:deploy_with_retry:810 - ==================================================
2025-05-30 09:48:37.003 | INFO     | __main__:deploy_with_retry:811 - Starting deployment attempt 1/1
2025-05-30 09:48:37.003 | INFO     | __main__:deploy_with_retry:817 - 📦 STEP 1/5: Model Registration
2025-05-30 09:48:37.003 | INFO     | __main__:register_model:428 - 🔍 MODEL REGISTRATION PHASE
2025-05-30 09:48:37.003 | INFO     | __main__:register_model:429 - ========================================
2025-05-30 09:48:37.003 | INFO     | __main__:register_model:430 - Checking for existing model: ps-dev-ca-tstarc
2025-05-30 09:48:37.003 | INFO     | __main__:_create_ml_client:330 - Creating Azure ML client connection
2025-05-30 09:48:38.832 | INFO     | __main__:_create_ml_client:345 - Successfully connected to workspace: ps-uat-mlw-claimsautoml
2025-05-30 09:48:38.952 | INFO     | __main__:register_model:442 - ℹ️  Model ps-dev-ca-tstarc not found, creating new registration
2025-05-30 09:48:38.952 | INFO     | __main__:register_model:446 - 📁 Validating model file: /home/<USER>/repos/autolodge_retrained_deploy/resources/models/ps-dev-claimsauto-tstarc.h5
2025-05-30 09:48:38.952 | ERROR    | __main__:register_model:481 - ❌ Failed to register model ps-dev-ca-tstarc: Model file not found: /home/<USER>/repos/autolodge_retrained_deploy/resources/models/ps-dev-claimsauto-tstarc.h5
2025-05-30 09:48:38.953 | ERROR    | __main__:deploy_with_retry:842 - ❌ Deployment attempt 1 failed: Failed to register model ps-dev-ca-tstarc: Model file not found: /home/<USER>/repos/autolodge_retrained_deploy/resources/models/ps-dev-claimsauto-tstarc.h5
2025-05-30 09:48:38.953 | ERROR    | __main__:deploy_with_retry:849 - 💥 All deployment attempts failed
2025-05-30 09:48:38.953 | ERROR    | __main__:deploy_with_retry:856 - 💥 Deployment failed after 1 attempts. Last error: Failed to register model ps-dev-ca-tstarc: Model file not found: /home/<USER>/repos/autolodge_retrained_deploy/resources/models/ps-dev-claimsauto-tstarc.h5
2025-05-30 09:48:38.953 | ERROR    | __main__:deployment_context:922 - Deployment context failed: Deployment failed after 1 attempts. Last error: Failed to register model ps-dev-ca-tstarc: Model file not found: /home/<USER>/repos/autolodge_retrained_deploy/resources/models/ps-dev-claimsauto-tstarc.h5
2025-05-30 09:48:38.953 | INFO     | __main__:deployment_context:925 - Deployment context cleanup completed
2025-05-30 09:48:38.953 | ERROR    | __main__:main:990 - ❌ Deployment failed: Deployment failed after 1 attempts. Last error: Failed to register model ps-dev-ca-tstarc: Model file not found: /home/<USER>/repos/autolodge_retrained_deploy/resources/models/ps-dev-claimsauto-tstarc.h5
2025-05-30 09:48:38.953 | ERROR    | __main__:main:991 - 💡 Check the log file for detailed error information
