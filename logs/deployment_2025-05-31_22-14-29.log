2025-05-31 22:14:29.770 | INFO     | __main__:setup_logging:98 - Logging configured - Log file: logs/deployment_2025-05-31_22-14-29.log
2025-05-31 22:14:29.771 | INFO     | __main__:deployment_context:955 - Starting deployment context
2025-05-31 22:14:29.771 | INFO     | __main__:main:975 - 🚀 AZURE ML DEPLOYMENT SCRIPT STARTED
2025-05-31 22:14:29.772 | INFO     | __main__:main:976 - ============================================================
2025-05-31 22:14:29.772 | INFO     | __main__:main:979 - 📋 STEP 1: Loading and validating configuration...
2025-05-31 22:14:29.774 | INFO     | __main__:load_and_validate_environment:943 - Environment configuration loaded and validated successfully
2025-05-31 22:14:29.774 | INFO     | __main__:main:981 - ✅ Configuration loaded successfully
2025-05-31 22:14:29.774 | INFO     | __main__:main:984 - 📊 STEP 2: Displaying deployment summary...
2025-05-31 22:14:29.774 | INFO     | __main__:display_deployment_summary:177 - ================================================================================
2025-05-31 22:14:29.774 | INFO     | __main__:display_deployment_summary:178 - DEPLOYMENT CONFIGURATION SUMMARY
2025-05-31 22:14:29.774 | INFO     | __main__:display_deployment_summary:179 - ================================================================================
2025-05-31 22:14:29.774 | INFO     | __main__:display_deployment_summary:182 - 🔧 Azure Environment:
2025-05-31 22:14:29.774 | INFO     | __main__:display_deployment_summary:183 -    Subscription ID: b15ae5d0-8f07-4cfb-aca3-508d38e9d983
2025-05-31 22:14:29.775 | INFO     | __main__:display_deployment_summary:184 -    Resource Group:  t-to-tstar-rg
2025-05-31 22:14:29.775 | INFO     | __main__:display_deployment_summary:185 -    Workspace:       t-to-tstar
2025-05-31 22:14:29.775 | INFO     | __main__:display_deployment_summary:188 - 🚀 Deployment Configuration:
2025-05-31 22:14:29.775 | INFO     | __main__:display_deployment_summary:189 -    Model Name:      ps-test-ca-tstar
2025-05-31 22:14:29.775 | INFO     | __main__:display_deployment_summary:190 -    Environment:     ps-test-ca-tstar
2025-05-31 22:14:29.775 | INFO     | __main__:display_deployment_summary:191 -    Endpoint Name:   ps-test-ca-tstar
2025-05-31 22:14:29.775 | INFO     | __main__:display_deployment_summary:192 -    Deployment Name: ps-test-ca-tstar
2025-05-31 22:14:29.775 | INFO     | __main__:display_deployment_summary:195 - 💻 Infrastructure:
2025-05-31 22:14:29.775 | INFO     | __main__:display_deployment_summary:196 -    Instance Type:   STANDARD_DS3_V2
2025-05-31 22:14:29.775 | INFO     | __main__:display_deployment_summary:197 -    Instance Count:  1
2025-05-31 22:14:29.775 | INFO     | __main__:display_deployment_summary:198 -    Timeout:         1800 seconds
2025-05-31 22:14:29.775 | INFO     | __main__:display_deployment_summary:199 -    Max Retries:     1
2025-05-31 22:14:29.775 | INFO     | __main__:display_deployment_summary:202 - 📁 File Validation:
2025-05-31 22:14:29.775 | INFO     | __main__:display_deployment_summary:208 -    Model File:      ✅ EXISTS (60.4 MB)
2025-05-31 22:14:29.776 | INFO     | __main__:display_deployment_summary:209 -    Model Path:      /home/<USER>/repos/autolodge_retrained_deploy/Python/resources/autolodge.h5
2025-05-31 22:14:29.776 | INFO     | __main__:display_deployment_summary:214 -    Conda File:      ✅ EXISTS
2025-05-31 22:14:29.776 | INFO     | __main__:display_deployment_summary:215 -    Conda Path:      /home/<USER>/repos/autolodge_retrained_deploy/configs/autolodge.yaml
2025-05-31 22:14:29.776 | INFO     | __main__:display_deployment_summary:220 -    Scoring Script:  ✅ EXISTS
2025-05-31 22:14:29.776 | INFO     | __main__:display_deployment_summary:221 -    Script Path:     /home/<USER>/repos/autolodge_retrained_deploy/Python/score.py
2025-05-31 22:14:29.776 | INFO     | __main__:display_deployment_summary:224 - 🔐 Environment Variables:
2025-05-31 22:14:29.776 | INFO     | __main__:display_deployment_summary:238 -    AZURE_SUBSCRIPTION_ID: ✅ b15ae5d0...
2025-05-31 22:14:29.776 | INFO     | __main__:display_deployment_summary:238 -    AZURE_RESOURCE_GROUP: ✅ t-to-tstar-rg
2025-05-31 22:14:29.776 | INFO     | __main__:display_deployment_summary:238 -    AZURE_ML_WORKSPACE_NAME: ✅ t-to-tstar
2025-05-31 22:14:29.776 | INFO     | __main__:display_deployment_summary:238 -    MODEL_NAME: ✅ ps-test-ca-tstar
2025-05-31 22:14:29.776 | INFO     | __main__:display_deployment_summary:238 -    ENV_NAME: ✅ ps-test-ca-tstar
2025-05-31 22:14:29.776 | INFO     | __main__:display_deployment_summary:238 -    ENDPOINT_NAME: ✅ ps-test-ca-tstar
2025-05-31 22:14:29.776 | INFO     | __main__:display_deployment_summary:238 -    DEPLOYMENT_NAME: ✅ ps-test-ca-tstar
2025-05-31 22:14:29.777 | INFO     | __main__:display_deployment_summary:240 - ================================================================================
2025-05-31 22:14:29.777 | INFO     | __main__:main:988 - 🔐 STEP 3: User confirmation required...
2025-05-31 22:14:29.777 | INFO     | __main__:get_user_confirmation:253 - ⚠️  DEPLOYMENT CONFIRMATION REQUIRED
2025-05-31 22:14:29.777 | INFO     | __main__:get_user_confirmation:254 - ==================================================
2025-05-31 22:14:29.777 | WARNING  | __main__:get_user_confirmation:257 - 🎯 Target Environment: t-to-tstar
2025-05-31 22:14:29.777 | WARNING  | __main__:get_user_confirmation:258 - 🚀 Deployment Name: ps-test-ca-tstar
2025-05-31 22:14:29.777 | WARNING  | __main__:get_user_confirmation:259 - 📦 Model: ps-test-ca-tstar
2025-05-31 22:14:29.777 | WARNING  | __main__:get_user_confirmation:260 - 💰 Instance Type: STANDARD_DS3_V2 (Count: 1)
2025-05-31 22:14:29.777 | INFO     | __main__:get_user_confirmation:263 - ⚠️  Potential Risks:
2025-05-31 22:14:29.777 | INFO     | __main__:get_user_confirmation:264 -    • This will create/update Azure ML resources
2025-05-31 22:14:29.777 | INFO     | __main__:get_user_confirmation:265 -    • Existing deployments with the same name may be overwritten
2025-05-31 22:14:29.777 | INFO     | __main__:get_user_confirmation:266 -    • Azure costs will be incurred for compute resources
2025-05-31 22:14:29.777 | INFO     | __main__:get_user_confirmation:267 -    • The deployment process may take 15-30 minutes
2025-05-31 22:14:29.777 | INFO     | __main__:get_user_confirmation:269 - ==================================================
2025-05-31 22:52:51.888 | INFO     | __main__:get_user_confirmation:278 - ✅ User confirmed deployment. Proceeding...
2025-05-31 22:52:51.888 | INFO     | __main__:main:994 - 🔧 STEP 4: Initializing Azure ML deployer...
2025-05-31 22:52:51.888 | INFO     | __main__:__init__:311 - Initialized AzureMLDeployer with configuration
2025-05-31 22:52:51.888 | INFO     | __main__:main:996 - ✅ Deployer initialized successfully
2025-05-31 22:52:51.888 | INFO     | __main__:main:999 - 🚀 STEP 5: Executing deployment...
2025-05-31 22:52:51.888 | INFO     | __main__:main:1000 - ⚠️  WARNING: Irreversible deployment operations will now begin!
2025-05-31 22:52:51.888 | INFO     | __main__:deploy_with_retry:847 - 🔄 DEPLOYMENT EXECUTION PHASE
2025-05-31 22:52:51.888 | INFO     | __main__:deploy_with_retry:848 - ==================================================
2025-05-31 22:52:51.888 | INFO     | __main__:deploy_with_retry:849 - Starting deployment attempt 1/1
2025-05-31 22:52:51.888 | INFO     | __main__:deploy_with_retry:855 - 📦 STEP 1/5: Model Registration
2025-05-31 22:52:51.889 | INFO     | __main__:register_model:429 - 🔍 MODEL REGISTRATION PHASE
2025-05-31 22:52:51.889 | INFO     | __main__:register_model:430 - ========================================
2025-05-31 22:52:51.889 | INFO     | __main__:register_model:431 - Checking for existing model: ps-test-ca-tstar
2025-05-31 22:52:51.889 | INFO     | __main__:_create_ml_client:331 - Creating Azure ML client connection
2025-05-31 22:52:52.618 | INFO     | __main__:_create_ml_client:346 - Successfully connected to workspace: t-to-tstar
2025-05-31 22:52:52.743 | INFO     | __main__:register_model:480 - ℹ️  Model ps-test-ca-tstar not found, creating new registration
2025-05-31 22:52:52.743 | INFO     | __main__:register_model:484 - 📁 Validating model file: /home/<USER>/repos/autolodge_retrained_deploy/Python/resources/autolodge.h5
2025-05-31 22:52:52.743 | INFO     | __main__:register_model:491 - ✅ Model file validation successful:
2025-05-31 22:52:52.743 | INFO     | __main__:register_model:492 -    File size: 60.37 MB
2025-05-31 22:52:52.743 | INFO     | __main__:register_model:493 -    Full path: /home/<USER>/repos/autolodge_retrained_deploy/Python/resources/autolodge.h5
2025-05-31 22:52:52.744 | INFO     | __main__:register_model:503 - 🚀 Registering new model from path: /home/<USER>/repos/autolodge_retrained_deploy/Python/resources/autolodge.h5
2025-05-31 22:52:52.744 | INFO     | __main__:register_model:504 -    Model name: ps-test-ca-tstar
2025-05-31 22:52:52.744 | INFO     | __main__:register_model:505 -    Model type: custom_model
2025-05-31 22:52:57.406 | INFO     | __main__:register_model:509 - ✅ Successfully registered model:
2025-05-31 22:52:57.406 | INFO     | __main__:register_model:510 -    Name: ps-test-ca-tstar
2025-05-31 22:52:57.406 | INFO     | __main__:register_model:511 -    Version: 1
2025-05-31 22:52:57.406 | INFO     | __main__:register_model:512 -    ID: /subscriptions/b15ae5d0-8f07-4cfb-aca3-508d38e9d983/resourceGroups/t-to-tstar-rg/providers/Microsoft.MachineLearningServices/workspaces/t-to-tstar/models/ps-test-ca-tstar/versions/1
2025-05-31 22:52:57.406 | INFO     | __main__:register_model:513 - ========================================
2025-05-31 22:52:57.406 | INFO     | __main__:deploy_with_retry:859 - 🌍 STEP 2/5: Environment Setup
2025-05-31 22:52:57.406 | INFO     | __main__:create_environment:533 - 🌍 ENVIRONMENT SETUP PHASE
2025-05-31 22:52:57.406 | INFO     | __main__:create_environment:534 - ========================================
2025-05-31 22:52:57.406 | INFO     | __main__:create_environment:535 - Checking for existing environment: ps-test-ca-tstar
2025-05-31 22:52:57.689 | INFO     | __main__:create_environment:547 - ℹ️  Environment ps-test-ca-tstar not found, creating new one
2025-05-31 22:52:57.690 | INFO     | __main__:create_environment:551 - 📁 Validating conda file: /home/<USER>/repos/autolodge_retrained_deploy/configs/autolodge.yaml
2025-05-31 22:52:57.690 | INFO     | __main__:create_environment:558 - ✅ Conda file validation successful:
2025-05-31 22:52:57.690 | INFO     | __main__:create_environment:559 -    File size: 472 bytes
2025-05-31 22:52:57.690 | INFO     | __main__:create_environment:560 -    Full path: /home/<USER>/repos/autolodge_retrained_deploy/configs/autolodge.yaml
2025-05-31 22:52:57.690 | INFO     | __main__:create_environment:566 -    Conda file preview (first 10 lines):
2025-05-31 22:52:57.690 | INFO     | __main__:create_environment:568 -      1: channels:
2025-05-31 22:52:57.690 | INFO     | __main__:create_environment:568 -      2: - Microsoft
2025-05-31 22:52:57.691 | INFO     | __main__:create_environment:568 -      3: - defaults
2025-05-31 22:52:57.691 | INFO     | __main__:create_environment:568 -      4: dependencies:
2025-05-31 22:52:57.691 | INFO     | __main__:create_environment:568 -      5: - pip
2025-05-31 22:52:57.691 | INFO     | __main__:create_environment:568 -      6: - python=3.9
2025-05-31 22:52:57.691 | INFO     | __main__:create_environment:568 -      7: - pip:
2025-05-31 22:52:57.691 | INFO     | __main__:create_environment:568 -      8: - azure-ai-ml==1.27.1
2025-05-31 22:52:57.691 | INFO     | __main__:create_environment:568 -      9: - azure-identity==1.23.0
2025-05-31 22:52:57.691 | INFO     | __main__:create_environment:568 -      10: - azureml-inference-server-http==1.4.0
2025-05-31 22:52:57.691 | INFO     | __main__:create_environment:570 -      ... (file continues)
2025-05-31 22:52:57.694 | INFO     | __main__:create_environment:583 - 🚀 Creating new environment:
2025-05-31 22:52:57.694 | INFO     | __main__:create_environment:584 -    Environment name: ps-test-ca-tstar
2025-05-31 22:52:57.694 | INFO     | __main__:create_environment:585 -    Base image: mcr.microsoft.com/azureml/openmpi3.1.2-ubuntu18.04
2025-05-31 22:52:57.695 | INFO     | __main__:create_environment:586 -    Conda file: /home/<USER>/repos/autolodge_retrained_deploy/configs/autolodge.yaml
2025-05-31 22:53:04.805 | INFO     | __main__:create_environment:590 - ✅ Successfully created environment:
2025-05-31 22:53:04.805 | INFO     | __main__:create_environment:591 -    Name: ps-test-ca-tstar
2025-05-31 22:53:04.805 | INFO     | __main__:create_environment:592 -    Version: 1
2025-05-31 22:53:04.806 | INFO     | __main__:create_environment:593 -    ID: /subscriptions/b15ae5d0-8f07-4cfb-aca3-508d38e9d983/resourceGroups/t-to-tstar-rg/providers/Microsoft.MachineLearningServices/workspaces/t-to-tstar/environments/ps-test-ca-tstar/versions/1
2025-05-31 22:53:04.806 | INFO     | __main__:create_environment:594 - ========================================
2025-05-31 22:53:04.806 | INFO     | __main__:deploy_with_retry:863 - 🌐 STEP 3/5: Endpoint Creation
2025-05-31 22:53:04.806 | INFO     | __main__:create_endpoint:614 - Checking for existing endpoint: ps-test-ca-tstar
2025-05-31 22:53:04.865 | INFO     | __main__:create_endpoint:634 - Endpoint ps-test-ca-tstar not found, creating new one
2025-05-31 22:53:04.867 | INFO     | __main__:create_endpoint:644 - Creating new endpoint: ps-test-ca-tstar
2025-05-31 22:53:07.265 | INFO     | __main__:_wait_for_operation_completion:390 - Starting endpoint creation operation (timeout: 1800s)
2025-05-31 22:53:07.265 | INFO     | __main__:_wait_for_operation_completion:403 - endpoint creation in progress... (0s elapsed)
2025-05-31 22:53:37.292 | INFO     | __main__:_wait_for_operation_completion:403 - endpoint creation in progress... (30s elapsed)
2025-05-31 22:54:07.321 | INFO     | __main__:_wait_for_operation_completion:403 - endpoint creation in progress... (60s elapsed)
2025-05-31 22:54:37.351 | INFO     | __main__:_wait_for_operation_completion:403 - endpoint creation in progress... (90s elapsed)
2025-05-31 22:54:42.357 | INFO     | __main__:_wait_for_operation_completion:409 - endpoint creation completed successfully in 95.1 seconds
2025-05-31 22:54:42.505 | INFO     | __main__:create_endpoint:652 - Successfully created endpoint: ps-test-ca-tstar
2025-05-31 22:54:42.505 | INFO     | __main__:deploy_with_retry:867 - 🚀 STEP 4/5: Deployment Creation
2025-05-31 22:54:42.505 | INFO     | __main__:create_deployment:690 - 🚀 DEPLOYMENT CREATION PHASE
2025-05-31 22:54:42.506 | INFO     | __main__:create_deployment:691 - ========================================
2025-05-31 22:54:42.506 | INFO     | __main__:create_deployment:697 - 📁 Validating deployment files:
2025-05-31 22:54:42.506 | INFO     | __main__:create_deployment:698 -    Scoring script: /home/<USER>/repos/autolodge_retrained_deploy/Python/score.py
2025-05-31 22:54:42.506 | INFO     | __main__:create_deployment:699 -    Code directory: /home/<USER>/repos/autolodge_retrained_deploy/Python
2025-05-31 22:54:42.506 | INFO     | __main__:create_deployment:706 - ✅ Scoring script validation successful:
2025-05-31 22:54:42.506 | INFO     | __main__:create_deployment:707 -    File size: 34.57 KB
2025-05-31 22:54:42.506 | INFO     | __main__:create_deployment:716 -    score.py: ✅ EXISTS
2025-05-31 22:54:42.506 | INFO     | __main__:create_deployment:721 -    resources/: ✅ EXISTS
2025-05-31 22:54:42.506 | INFO     | __main__:create_deployment:721 -    logs/: ✅ EXISTS
2025-05-31 22:54:42.506 | INFO     | __main__:create_deployment:723 - 🔧 Deployment configuration:
2025-05-31 22:54:42.506 | INFO     | __main__:create_deployment:724 -    Deployment name: ps-test-ca-tstar
2025-05-31 22:54:42.506 | INFO     | __main__:create_deployment:725 -    Endpoint name: ps-test-ca-tstar
2025-05-31 22:54:42.507 | INFO     | __main__:create_deployment:726 -    Model: ps-test-ca-tstar (v1)
2025-05-31 22:54:42.507 | INFO     | __main__:create_deployment:727 -    Environment: ps-test-ca-tstar (v1)
2025-05-31 22:54:42.507 | INFO     | __main__:create_deployment:728 -    Instance type: STANDARD_DS3_V2
2025-05-31 22:54:42.507 | INFO     | __main__:create_deployment:729 -    Instance count: 1
2025-05-31 22:54:42.507 | INFO     | __main__:create_deployment:742 - 🏷️  Environment variables:
2025-05-31 22:54:42.507 | INFO     | __main__:create_deployment:744 -    PYTHONPATH: /var/azureml-app/autolodge_retrained_deploy
2025-05-31 22:54:42.507 | INFO     | __main__:create_deployment:746 - 🏷️  Deployment tags:
2025-05-31 22:54:42.507 | INFO     | __main__:create_deployment:748 -    DeploymentDate: 2025-05-31T22:54:42.507270+10:00
2025-05-31 22:54:42.507 | INFO     | __main__:create_deployment:748 -    ModelName: ps-test-ca-tstar
2025-05-31 22:54:42.507 | INFO     | __main__:create_deployment:748 -    ModelVersion: 1
2025-05-31 22:54:42.507 | INFO     | __main__:create_deployment:748 -    EnvironmentName: ps-test-ca-tstar
2025-05-31 22:54:42.507 | INFO     | __main__:create_deployment:748 -    EnvironmentVersion: 1
2025-05-31 22:54:42.507 | INFO     | __main__:create_deployment:748 -    CreatedBy: AzureMLDeployer
2025-05-31 22:54:42.508 | INFO     | __main__:create_deployment:763 - 🚀 Starting deployment creation...
2025-05-31 22:54:42.508 | WARNING  | __main__:create_deployment:764 - ⏱️  This operation may take 15-30 minutes to complete
2025-05-31 22:54:50.824 | INFO     | __main__:_wait_for_operation_completion:390 - Starting deployment creation operation (timeout: 1800s)
2025-05-31 22:54:50.825 | INFO     | __main__:_wait_for_operation_completion:403 - deployment creation in progress... (0s elapsed)
2025-05-31 22:55:20.850 | INFO     | __main__:_wait_for_operation_completion:403 - deployment creation in progress... (30s elapsed)
2025-05-31 22:55:50.878 | INFO     | __main__:_wait_for_operation_completion:403 - deployment creation in progress... (60s elapsed)
2025-05-31 22:56:20.904 | INFO     | __main__:_wait_for_operation_completion:403 - deployment creation in progress... (90s elapsed)
2025-05-31 22:56:50.934 | INFO     | __main__:_wait_for_operation_completion:403 - deployment creation in progress... (120s elapsed)
2025-05-31 22:57:20.959 | INFO     | __main__:_wait_for_operation_completion:403 - deployment creation in progress... (150s elapsed)
2025-05-31 22:57:50.986 | INFO     | __main__:_wait_for_operation_completion:403 - deployment creation in progress... (180s elapsed)
2025-05-31 22:58:21.008 | INFO     | __main__:_wait_for_operation_completion:403 - deployment creation in progress... (210s elapsed)
2025-05-31 22:58:51.037 | INFO     | __main__:_wait_for_operation_completion:403 - deployment creation in progress... (240s elapsed)
2025-05-31 22:59:21.067 | INFO     | __main__:_wait_for_operation_completion:403 - deployment creation in progress... (270s elapsed)
2025-05-31 22:59:51.094 | INFO     | __main__:_wait_for_operation_completion:403 - deployment creation in progress... (300s elapsed)
2025-05-31 23:00:21.120 | INFO     | __main__:_wait_for_operation_completion:403 - deployment creation in progress... (330s elapsed)
2025-05-31 23:00:51.143 | INFO     | __main__:_wait_for_operation_completion:403 - deployment creation in progress... (360s elapsed)
2025-05-31 23:01:21.169 | INFO     | __main__:_wait_for_operation_completion:403 - deployment creation in progress... (390s elapsed)
2025-05-31 23:01:51.196 | INFO     | __main__:_wait_for_operation_completion:403 - deployment creation in progress... (420s elapsed)
2025-05-31 23:02:21.226 | INFO     | __main__:_wait_for_operation_completion:403 - deployment creation in progress... (450s elapsed)
2025-05-31 23:02:51.254 | INFO     | __main__:_wait_for_operation_completion:403 - deployment creation in progress... (480s elapsed)
2025-05-31 23:03:21.284 | INFO     | __main__:_wait_for_operation_completion:403 - deployment creation in progress... (510s elapsed)
2025-05-31 23:03:51.310 | INFO     | __main__:_wait_for_operation_completion:403 - deployment creation in progress... (540s elapsed)
2025-05-31 23:04:21.339 | INFO     | __main__:_wait_for_operation_completion:403 - deployment creation in progress... (571s elapsed)
2025-05-31 23:04:51.365 | INFO     | __main__:_wait_for_operation_completion:403 - deployment creation in progress... (601s elapsed)
2025-05-31 23:05:21.395 | INFO     | __main__:_wait_for_operation_completion:403 - deployment creation in progress... (631s elapsed)
2025-05-31 23:05:51.425 | INFO     | __main__:_wait_for_operation_completion:403 - deployment creation in progress... (661s elapsed)
2025-05-31 23:06:21.454 | INFO     | __main__:_wait_for_operation_completion:403 - deployment creation in progress... (691s elapsed)
2025-05-31 23:06:51.484 | INFO     | __main__:_wait_for_operation_completion:403 - deployment creation in progress... (721s elapsed)
2025-05-31 23:07:21.516 | INFO     | __main__:_wait_for_operation_completion:403 - deployment creation in progress... (751s elapsed)
2025-05-31 23:07:51.545 | INFO     | __main__:_wait_for_operation_completion:403 - deployment creation in progress... (781s elapsed)
2025-05-31 23:08:21.571 | INFO     | __main__:_wait_for_operation_completion:403 - deployment creation in progress... (811s elapsed)
2025-05-31 23:08:51.589 | INFO     | __main__:_wait_for_operation_completion:403 - deployment creation in progress... (841s elapsed)
2025-05-31 23:09:21.619 | INFO     | __main__:_wait_for_operation_completion:403 - deployment creation in progress... (871s elapsed)
2025-05-31 23:09:51.645 | INFO     | __main__:_wait_for_operation_completion:403 - deployment creation in progress... (901s elapsed)
2025-05-31 23:10:21.673 | INFO     | __main__:_wait_for_operation_completion:403 - deployment creation in progress... (931s elapsed)
2025-05-31 23:10:26.679 | INFO     | __main__:_wait_for_operation_completion:409 - deployment creation completed successfully in 935.9 seconds
2025-05-31 23:10:26.771 | INFO     | __main__:create_deployment:777 - ✅ Successfully created deployment:
2025-05-31 23:10:26.771 | INFO     | __main__:create_deployment:778 -    Name: ps-test-ca-tstar
2025-05-31 23:10:26.771 | INFO     | __main__:create_deployment:779 -    Endpoint: ps-test-ca-tstar
2025-05-31 23:10:26.771 | INFO     | __main__:create_deployment:780 -    Status: Succeeded
2025-05-31 23:10:26.771 | INFO     | __main__:create_deployment:781 - ========================================
2025-05-31 23:10:26.771 | INFO     | __main__:deploy_with_retry:871 - 🚦 STEP 5/5: Traffic Configuration
2025-05-31 23:10:26.771 | INFO     | __main__:configure_traffic:810 - Configuring traffic for deployment: ps-test-ca-tstar
2025-05-31 23:10:28.153 | INFO     | __main__:_wait_for_operation_completion:390 - Starting traffic configuration operation (timeout: 1800s)
2025-05-31 23:10:28.157 | INFO     | __main__:_wait_for_operation_completion:403 - traffic configuration in progress... (0s elapsed)
2025-05-31 23:10:58.184 | INFO     | __main__:_wait_for_operation_completion:403 - traffic configuration in progress... (30s elapsed)
2025-05-31 23:11:03.189 | INFO     | __main__:_wait_for_operation_completion:409 - traffic configuration completed successfully in 35.0 seconds
2025-05-31 23:11:03.728 | INFO     | __main__:configure_traffic:825 - Traffic configuration completed: {'ps-test-ca-tstar': 100}
2025-05-31 23:11:03.731 | INFO     | __main__:deploy_with_retry:874 - ✅ All deployment steps completed successfully!
2025-05-31 23:11:03.731 | INFO     | __main__:deploy_with_retry:875 - ==================================================
2025-05-31 23:11:03.731 | INFO     | __main__:main:1004 - 🎉 DEPLOYMENT COMPLETED SUCCESSFULLY!
2025-05-31 23:11:03.732 | INFO     | __main__:main:1005 - ============================================================
2025-05-31 23:11:03.732 | INFO     | __main__:main:1006 - FINAL DEPLOYMENT SUMMARY
2025-05-31 23:11:03.732 | INFO     | __main__:main:1007 - ============================================================
2025-05-31 23:11:03.732 | INFO     | __main__:main:1008 - ✅ Endpoint: ps-test-ca-tstar
2025-05-31 23:11:03.732 | INFO     | __main__:main:1009 - ✅ Deployment: ps-test-ca-tstar
2025-05-31 23:11:03.732 | INFO     | __main__:main:1010 - ✅ Traffic Configuration: {'ps-test-ca-tstar': 100}
2025-05-31 23:11:03.732 | INFO     | __main__:main:1011 - ✅ Instance Type: STANDARD_DS3_V2
2025-05-31 23:11:03.734 | INFO     | __main__:main:1012 - ✅ Instance Count: 1
2025-05-31 23:11:03.734 | INFO     | __main__:main:1013 - ✅ Workspace: t-to-tstar
2025-05-31 23:11:03.734 | INFO     | __main__:main:1014 - ✅ Resource Group: t-to-tstar-rg
2025-05-31 23:11:03.734 | INFO     | __main__:main:1015 - ============================================================
2025-05-31 23:11:03.734 | INFO     | __main__:main:1018 - 📋 NEXT STEPS:
2025-05-31 23:11:03.734 | INFO     | __main__:main:1019 -    1. Test the deployed endpoint with sample data
2025-05-31 23:11:03.735 | INFO     | __main__:main:1020 -    2. Monitor deployment performance and logs
2025-05-31 23:11:03.735 | INFO     | __main__:main:1021 -    3. Update traffic allocation if needed
2025-05-31 23:11:03.735 | INFO     | __main__:main:1022 -    4. Set up monitoring and alerting
2025-05-31 23:11:03.735 | INFO     | __main__:main:1023 - ============================================================
2025-05-31 23:11:03.735 | SUCCESS  | __main__:main:1025 - 🎉 Deployment completed successfully!
2025-05-31 23:11:03.735 | INFO     | __main__:deployment_context:958 - Deployment context completed successfully
2025-05-31 23:11:03.735 | INFO     | __main__:deployment_context:963 - Deployment context cleanup completed
