2025-05-30 12:37:20.686 | INFO     | __main__:setup_logging:98 - Logging configured - Log file: logs/deployment_2025-05-30_12-37-20.log
2025-05-30 12:37:20.687 | INFO     | __main__:deployment_context:954 - Starting deployment context
2025-05-30 12:37:20.687 | INFO     | __main__:main:974 - 🚀 AZURE ML DEPLOYMENT SCRIPT STARTED
2025-05-30 12:37:20.687 | INFO     | __main__:main:975 - ============================================================
2025-05-30 12:37:20.687 | INFO     | __main__:main:978 - 📋 STEP 1: Loading and validating configuration...
2025-05-30 12:37:20.689 | INFO     | __main__:load_and_validate_environment:942 - Environment configuration loaded and validated successfully
2025-05-30 12:37:20.689 | INFO     | __main__:main:980 - ✅ Configuration loaded successfully
2025-05-30 12:37:20.689 | INFO     | __main__:main:983 - 📊 STEP 2: Displaying deployment summary...
2025-05-30 12:37:20.689 | INFO     | __main__:display_deployment_summary:176 - ================================================================================
2025-05-30 12:37:20.689 | INFO     | __main__:display_deployment_summary:177 - DEPLOYMENT CONFIGURATION SUMMARY
2025-05-30 12:37:20.689 | INFO     | __main__:display_deployment_summary:178 - ================================================================================
2025-05-30 12:37:20.689 | INFO     | __main__:display_deployment_summary:181 - 🔧 Azure Environment:
2025-05-30 12:37:20.690 | INFO     | __main__:display_deployment_summary:182 -    Subscription ID: b15ae5d0-8f07-4cfb-aca3-508d38e9d983
2025-05-30 12:37:20.690 | INFO     | __main__:display_deployment_summary:183 -    Resource Group:  ps-dev-rg-claimsautoml
2025-05-30 12:37:20.690 | INFO     | __main__:display_deployment_summary:184 -    Workspace:       ps-dev-mlw-claimsauto
2025-05-30 12:37:20.690 | INFO     | __main__:display_deployment_summary:187 - 🚀 Deployment Configuration:
2025-05-30 12:37:20.690 | INFO     | __main__:display_deployment_summary:188 -    Model Name:      ps-dev-ca-tstar
2025-05-30 12:37:20.690 | INFO     | __main__:display_deployment_summary:189 -    Environment:     ps-dev-ca-tstar
2025-05-30 12:37:20.690 | INFO     | __main__:display_deployment_summary:190 -    Endpoint Name:   ps-dev-ca-tstar
2025-05-30 12:37:20.690 | INFO     | __main__:display_deployment_summary:191 -    Deployment Name: ps-dev-ca-tstar
2025-05-30 12:37:20.690 | INFO     | __main__:display_deployment_summary:194 - 💻 Infrastructure:
2025-05-30 12:37:20.690 | INFO     | __main__:display_deployment_summary:195 -    Instance Type:   STANDARD_NC4AS_T4_V3
2025-05-30 12:37:20.690 | INFO     | __main__:display_deployment_summary:196 -    Instance Count:  1
2025-05-30 12:37:20.690 | INFO     | __main__:display_deployment_summary:197 -    Timeout:         1800 seconds
2025-05-30 12:37:20.690 | INFO     | __main__:display_deployment_summary:198 -    Max Retries:     1
2025-05-30 12:37:20.690 | INFO     | __main__:display_deployment_summary:201 - 📁 File Validation:
2025-05-30 12:37:20.691 | INFO     | __main__:display_deployment_summary:207 -    Model File:      ✅ EXISTS (60.4 MB)
2025-05-30 12:37:20.691 | INFO     | __main__:display_deployment_summary:208 -    Model Path:      /home/<USER>/repos/autolodge_retrained_deploy/data/models/autolodge.h5
2025-05-30 12:37:20.691 | INFO     | __main__:display_deployment_summary:213 -    Conda File:      ✅ EXISTS
2025-05-30 12:37:20.691 | INFO     | __main__:display_deployment_summary:214 -    Conda Path:      /home/<USER>/repos/autolodge_retrained_deploy/configs/autolodge.yaml
2025-05-30 12:37:20.691 | INFO     | __main__:display_deployment_summary:219 -    Scoring Script:  ✅ EXISTS
2025-05-30 12:37:20.691 | INFO     | __main__:display_deployment_summary:220 -    Script Path:     /home/<USER>/repos/autolodge_retrained_deploy/Python/score.py
2025-05-30 12:37:20.691 | INFO     | __main__:display_deployment_summary:223 - 🔐 Environment Variables:
2025-05-30 12:37:20.691 | INFO     | __main__:display_deployment_summary:237 -    AZURE_SUBSCRIPTION_ID: ✅ b15ae5d0...
2025-05-30 12:37:20.691 | INFO     | __main__:display_deployment_summary:237 -    AZURE_RESOURCE_GROUP: ✅ ps-dev-rg-claimsautoml
2025-05-30 12:37:20.691 | INFO     | __main__:display_deployment_summary:237 -    AZURE_ML_WORKSPACE_NAME: ✅ ps-dev-mlw-claimsauto
2025-05-30 12:37:20.691 | INFO     | __main__:display_deployment_summary:237 -    MODEL_NAME: ✅ ps-dev-ca-tstar
2025-05-30 12:37:20.691 | INFO     | __main__:display_deployment_summary:237 -    ENV_NAME: ✅ ps-dev-ca-tstar
2025-05-30 12:37:20.691 | INFO     | __main__:display_deployment_summary:237 -    ENDPOINT_NAME: ✅ ps-dev-ca-tstar
2025-05-30 12:37:20.692 | INFO     | __main__:display_deployment_summary:237 -    DEPLOYMENT_NAME: ✅ ps-dev-ca-tstar
2025-05-30 12:37:20.692 | INFO     | __main__:display_deployment_summary:239 - ================================================================================
2025-05-30 12:37:20.692 | INFO     | __main__:main:987 - 🔐 STEP 3: User confirmation required...
2025-05-30 12:37:20.692 | INFO     | __main__:get_user_confirmation:252 - ⚠️  DEPLOYMENT CONFIRMATION REQUIRED
2025-05-30 12:37:20.692 | INFO     | __main__:get_user_confirmation:253 - ==================================================
2025-05-30 12:37:20.692 | WARNING  | __main__:get_user_confirmation:256 - 🎯 Target Environment: ps-dev-mlw-claimsauto
2025-05-30 12:37:20.692 | WARNING  | __main__:get_user_confirmation:257 - 🚀 Deployment Name: ps-dev-ca-tstar
2025-05-30 12:37:20.692 | WARNING  | __main__:get_user_confirmation:258 - 📦 Model: ps-dev-ca-tstar
2025-05-30 12:37:20.692 | WARNING  | __main__:get_user_confirmation:259 - 💰 Instance Type: STANDARD_NC4AS_T4_V3 (Count: 1)
2025-05-30 12:37:20.692 | INFO     | __main__:get_user_confirmation:262 - ⚠️  Potential Risks:
2025-05-30 12:37:20.692 | INFO     | __main__:get_user_confirmation:263 -    • This will create/update Azure ML resources
2025-05-30 12:37:20.692 | INFO     | __main__:get_user_confirmation:264 -    • Existing deployments with the same name may be overwritten
2025-05-30 12:37:20.692 | INFO     | __main__:get_user_confirmation:265 -    • Azure costs will be incurred for compute resources
2025-05-30 12:37:20.692 | INFO     | __main__:get_user_confirmation:266 -    • The deployment process may take 15-30 minutes
2025-05-30 12:37:20.692 | INFO     | __main__:get_user_confirmation:268 - ==================================================
2025-05-30 12:37:27.378 | INFO     | __main__:get_user_confirmation:277 - ✅ User confirmed deployment. Proceeding...
2025-05-30 12:37:27.378 | INFO     | __main__:main:993 - 🔧 STEP 4: Initializing Azure ML deployer...
2025-05-30 12:37:27.378 | INFO     | __main__:__init__:310 - Initialized AzureMLDeployer with configuration
2025-05-30 12:37:27.378 | INFO     | __main__:main:995 - ✅ Deployer initialized successfully
2025-05-30 12:37:27.378 | INFO     | __main__:main:998 - 🚀 STEP 5: Executing deployment...
2025-05-30 12:37:27.378 | INFO     | __main__:main:999 - ⚠️  WARNING: Irreversible deployment operations will now begin!
2025-05-30 12:37:27.378 | INFO     | __main__:deploy_with_retry:846 - 🔄 DEPLOYMENT EXECUTION PHASE
2025-05-30 12:37:27.379 | INFO     | __main__:deploy_with_retry:847 - ==================================================
2025-05-30 12:37:27.379 | INFO     | __main__:deploy_with_retry:848 - Starting deployment attempt 1/1
2025-05-30 12:37:27.379 | INFO     | __main__:deploy_with_retry:854 - 📦 STEP 1/5: Model Registration
2025-05-30 12:37:27.379 | INFO     | __main__:register_model:428 - 🔍 MODEL REGISTRATION PHASE
2025-05-30 12:37:27.379 | INFO     | __main__:register_model:429 - ========================================
2025-05-30 12:37:27.379 | INFO     | __main__:register_model:430 - Checking for existing model: ps-dev-ca-tstar
2025-05-30 12:37:27.379 | INFO     | __main__:_create_ml_client:330 - Creating Azure ML client connection
2025-05-30 12:37:28.003 | INFO     | __main__:_create_ml_client:345 - Successfully connected to workspace: ps-dev-mlw-claimsauto
2025-05-30 12:37:28.574 | INFO     | __main__:register_model:479 - ℹ️  Model ps-dev-ca-tstar not found, creating new registration
2025-05-30 12:37:28.575 | INFO     | __main__:register_model:483 - 📁 Validating model file: /home/<USER>/repos/autolodge_retrained_deploy/data/models/autolodge.h5
2025-05-30 12:37:28.575 | INFO     | __main__:register_model:490 - ✅ Model file validation successful:
2025-05-30 12:37:28.575 | INFO     | __main__:register_model:491 -    File size: 60.37 MB
2025-05-30 12:37:28.575 | INFO     | __main__:register_model:492 -    Full path: /home/<USER>/repos/autolodge_retrained_deploy/data/models/autolodge.h5
2025-05-30 12:37:28.575 | INFO     | __main__:register_model:502 - 🚀 Registering new model from path: /home/<USER>/repos/autolodge_retrained_deploy/data/models/autolodge.h5
2025-05-30 12:37:28.575 | INFO     | __main__:register_model:503 -    Model name: ps-dev-ca-tstar
2025-05-30 12:37:28.575 | INFO     | __main__:register_model:504 -    Model type: custom_model
2025-05-30 12:37:32.811 | INFO     | __main__:register_model:508 - ✅ Successfully registered model:
2025-05-30 12:37:32.811 | INFO     | __main__:register_model:509 -    Name: ps-dev-ca-tstar
2025-05-30 12:37:32.811 | INFO     | __main__:register_model:510 -    Version: 1
2025-05-30 12:37:32.811 | INFO     | __main__:register_model:511 -    ID: /subscriptions/b15ae5d0-8f07-4cfb-aca3-508d38e9d983/resourceGroups/ps-dev-rg-claimsautoml/providers/Microsoft.MachineLearningServices/workspaces/ps-dev-mlw-claimsauto/models/ps-dev-ca-tstar/versions/1
2025-05-30 12:37:32.812 | INFO     | __main__:register_model:512 - ========================================
2025-05-30 12:37:32.812 | INFO     | __main__:deploy_with_retry:858 - 🌍 STEP 2/5: Environment Setup
2025-05-30 12:37:32.812 | INFO     | __main__:create_environment:532 - 🌍 ENVIRONMENT SETUP PHASE
2025-05-30 12:37:32.812 | INFO     | __main__:create_environment:533 - ========================================
2025-05-30 12:37:32.812 | INFO     | __main__:create_environment:534 - Checking for existing environment: ps-dev-ca-tstar
2025-05-30 12:37:33.120 | INFO     | __main__:create_environment:546 - ℹ️  Environment ps-dev-ca-tstar not found, creating new one
2025-05-30 12:37:33.120 | INFO     | __main__:create_environment:550 - 📁 Validating conda file: /home/<USER>/repos/autolodge_retrained_deploy/configs/autolodge.yaml
2025-05-30 12:37:33.120 | INFO     | __main__:create_environment:557 - ✅ Conda file validation successful:
2025-05-30 12:37:33.120 | INFO     | __main__:create_environment:558 -    File size: 472 bytes
2025-05-30 12:37:33.120 | INFO     | __main__:create_environment:559 -    Full path: /home/<USER>/repos/autolodge_retrained_deploy/configs/autolodge.yaml
2025-05-30 12:37:33.121 | INFO     | __main__:create_environment:565 -    Conda file preview (first 10 lines):
2025-05-30 12:37:33.121 | INFO     | __main__:create_environment:567 -      1: channels:
2025-05-30 12:37:33.121 | INFO     | __main__:create_environment:567 -      2: - Microsoft
2025-05-30 12:37:33.121 | INFO     | __main__:create_environment:567 -      3: - defaults
2025-05-30 12:37:33.121 | INFO     | __main__:create_environment:567 -      4: dependencies:
2025-05-30 12:37:33.121 | INFO     | __main__:create_environment:567 -      5: - pip
2025-05-30 12:37:33.121 | INFO     | __main__:create_environment:567 -      6: - python=3.9
2025-05-30 12:37:33.121 | INFO     | __main__:create_environment:567 -      7: - pip:
2025-05-30 12:37:33.121 | INFO     | __main__:create_environment:567 -      8: - azure-ai-ml==1.27.1
2025-05-30 12:37:33.121 | INFO     | __main__:create_environment:567 -      9: - azure-identity==1.23.0
2025-05-30 12:37:33.121 | INFO     | __main__:create_environment:567 -      10: - azureml-inference-server-http==1.4.0
2025-05-30 12:37:33.121 | INFO     | __main__:create_environment:569 -      ... (file continues)
2025-05-30 12:37:33.125 | INFO     | __main__:create_environment:582 - 🚀 Creating new environment:
2025-05-30 12:37:33.125 | INFO     | __main__:create_environment:583 -    Environment name: ps-dev-ca-tstar
2025-05-30 12:37:33.125 | INFO     | __main__:create_environment:584 -    Base image: mcr.microsoft.com/azureml/openmpi3.1.2-ubuntu18.04
2025-05-30 12:37:33.125 | INFO     | __main__:create_environment:585 -    Conda file: /home/<USER>/repos/autolodge_retrained_deploy/configs/autolodge.yaml
2025-05-30 12:37:40.370 | INFO     | __main__:create_environment:589 - ✅ Successfully created environment:
2025-05-30 12:37:40.370 | INFO     | __main__:create_environment:590 -    Name: ps-dev-ca-tstar
2025-05-30 12:37:40.370 | INFO     | __main__:create_environment:591 -    Version: 1
2025-05-30 12:37:40.371 | INFO     | __main__:create_environment:592 -    ID: /subscriptions/b15ae5d0-8f07-4cfb-aca3-508d38e9d983/resourceGroups/ps-dev-rg-claimsautoml/providers/Microsoft.MachineLearningServices/workspaces/ps-dev-mlw-claimsauto/environments/ps-dev-ca-tstar/versions/1
2025-05-30 12:37:40.371 | INFO     | __main__:create_environment:593 - ========================================
2025-05-30 12:37:40.371 | INFO     | __main__:deploy_with_retry:862 - 🌐 STEP 3/5: Endpoint Creation
2025-05-30 12:37:40.371 | INFO     | __main__:create_endpoint:613 - Checking for existing endpoint: ps-dev-ca-tstar
2025-05-30 12:37:40.425 | INFO     | __main__:create_endpoint:633 - Endpoint ps-dev-ca-tstar not found, creating new one
2025-05-30 12:37:40.426 | INFO     | __main__:create_endpoint:643 - Creating new endpoint: ps-dev-ca-tstar
2025-05-30 12:37:42.698 | INFO     | __main__:_wait_for_operation_completion:389 - Starting endpoint creation operation (timeout: 1800s)
2025-05-30 12:37:42.698 | INFO     | __main__:_wait_for_operation_completion:402 - endpoint creation in progress... (0s elapsed)
2025-05-30 12:38:12.727 | INFO     | __main__:_wait_for_operation_completion:402 - endpoint creation in progress... (30s elapsed)
2025-05-30 12:38:42.745 | INFO     | __main__:_wait_for_operation_completion:402 - endpoint creation in progress... (60s elapsed)
2025-05-30 12:38:47.751 | INFO     | __main__:_wait_for_operation_completion:408 - endpoint creation completed successfully in 65.1 seconds
2025-05-30 12:38:48.028 | INFO     | __main__:create_endpoint:651 - Successfully created endpoint: ps-dev-ca-tstar
2025-05-30 12:38:48.028 | INFO     | __main__:deploy_with_retry:866 - 🚀 STEP 4/5: Deployment Creation
2025-05-30 12:38:48.028 | INFO     | __main__:create_deployment:689 - 🚀 DEPLOYMENT CREATION PHASE
2025-05-30 12:38:48.028 | INFO     | __main__:create_deployment:690 - ========================================
2025-05-30 12:38:48.028 | INFO     | __main__:create_deployment:696 - 📁 Validating deployment files:
2025-05-30 12:38:48.028 | INFO     | __main__:create_deployment:697 -    Scoring script: /home/<USER>/repos/autolodge_retrained_deploy/Python/score.py
2025-05-30 12:38:48.028 | INFO     | __main__:create_deployment:698 -    Code directory: /home/<USER>/repos/autolodge_retrained_deploy/Python
2025-05-30 12:38:48.028 | INFO     | __main__:create_deployment:705 - ✅ Scoring script validation successful:
2025-05-30 12:38:48.028 | INFO     | __main__:create_deployment:706 -    File size: 34.88 KB
2025-05-30 12:38:48.029 | INFO     | __main__:create_deployment:715 -    score.py: ✅ EXISTS
2025-05-30 12:38:48.029 | INFO     | __main__:create_deployment:720 -    resources/: ✅ EXISTS
2025-05-30 12:38:48.029 | INFO     | __main__:create_deployment:720 -    logs/: ✅ EXISTS
2025-05-30 12:38:48.029 | INFO     | __main__:create_deployment:722 - 🔧 Deployment configuration:
2025-05-30 12:38:48.029 | INFO     | __main__:create_deployment:723 -    Deployment name: ps-dev-ca-tstar
2025-05-30 12:38:48.029 | INFO     | __main__:create_deployment:724 -    Endpoint name: ps-dev-ca-tstar
2025-05-30 12:38:48.029 | INFO     | __main__:create_deployment:725 -    Model: ps-dev-ca-tstar (v1)
2025-05-30 12:38:48.029 | INFO     | __main__:create_deployment:726 -    Environment: ps-dev-ca-tstar (v1)
2025-05-30 12:38:48.029 | INFO     | __main__:create_deployment:727 -    Instance type: STANDARD_NC4AS_T4_V3
2025-05-30 12:38:48.029 | INFO     | __main__:create_deployment:728 -    Instance count: 1
2025-05-30 12:38:48.029 | INFO     | __main__:create_deployment:741 - 🏷️  Environment variables:
2025-05-30 12:38:48.029 | INFO     | __main__:create_deployment:743 -    PYTHONPATH: /var/azureml-app/autolodge_retrained_deploy
2025-05-30 12:38:48.029 | INFO     | __main__:create_deployment:745 - 🏷️  Deployment tags:
2025-05-30 12:38:48.030 | INFO     | __main__:create_deployment:747 -    DeploymentDate: 2025-05-30T12:38:48.029827+10:00
2025-05-30 12:38:48.030 | INFO     | __main__:create_deployment:747 -    ModelName: ps-dev-ca-tstar
2025-05-30 12:38:48.030 | INFO     | __main__:create_deployment:747 -    ModelVersion: 1
2025-05-30 12:38:48.030 | INFO     | __main__:create_deployment:747 -    EnvironmentName: ps-dev-ca-tstar
2025-05-30 12:38:48.030 | INFO     | __main__:create_deployment:747 -    EnvironmentVersion: 1
2025-05-30 12:38:48.030 | INFO     | __main__:create_deployment:747 -    CreatedBy: AzureMLDeployer
2025-05-30 12:38:48.030 | INFO     | __main__:create_deployment:762 - 🚀 Starting deployment creation...
2025-05-30 12:38:48.030 | WARNING  | __main__:create_deployment:763 - ⏱️  This operation may take 15-30 minutes to complete
2025-05-30 12:38:55.663 | INFO     | __main__:_wait_for_operation_completion:389 - Starting deployment creation operation (timeout: 1800s)
2025-05-30 12:38:55.663 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (0s elapsed)
2025-05-30 12:39:25.693 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (30s elapsed)
2025-05-30 12:39:55.722 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (60s elapsed)
2025-05-30 12:40:25.751 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (90s elapsed)
2025-05-30 12:40:55.778 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (120s elapsed)
2025-05-30 12:41:25.808 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (150s elapsed)
2025-05-30 12:41:55.838 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (180s elapsed)
2025-05-30 12:42:25.870 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (210s elapsed)
2025-05-30 12:42:55.900 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (240s elapsed)
2025-05-30 12:43:25.927 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (270s elapsed)
2025-05-30 12:43:55.952 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (300s elapsed)
2025-05-30 12:44:25.980 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (330s elapsed)
2025-05-30 12:44:56.005 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (360s elapsed)
2025-05-30 12:45:26.035 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (390s elapsed)
2025-05-30 12:45:56.060 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (420s elapsed)
2025-05-30 12:46:26.090 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (450s elapsed)
2025-05-30 12:46:56.116 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (480s elapsed)
2025-05-30 12:47:26.142 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (510s elapsed)
2025-05-30 12:47:56.163 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (541s elapsed)
2025-05-30 12:48:26.184 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (571s elapsed)
2025-05-30 12:48:56.211 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (601s elapsed)
2025-05-30 12:49:26.240 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (631s elapsed)
2025-05-30 12:49:56.269 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (661s elapsed)
2025-05-30 12:50:26.298 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (691s elapsed)
2025-05-30 12:50:56.325 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (721s elapsed)
2025-05-30 12:51:26.348 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (751s elapsed)
2025-05-30 12:51:56.375 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (781s elapsed)
2025-05-30 12:52:26.406 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (811s elapsed)
2025-05-30 12:52:56.433 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (841s elapsed)
2025-05-30 12:53:26.459 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (871s elapsed)
2025-05-30 12:53:56.488 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (901s elapsed)
2025-05-30 12:54:26.515 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (931s elapsed)
2025-05-30 12:54:56.542 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (961s elapsed)
2025-05-30 12:55:26.568 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (991s elapsed)
2025-05-30 12:55:56.598 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (1021s elapsed)
2025-05-30 12:56:26.623 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (1051s elapsed)
2025-05-30 12:56:56.650 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (1081s elapsed)
2025-05-30 12:57:26.682 | INFO     | __main__:_wait_for_operation_completion:408 - deployment creation completed successfully in 1111.0 seconds
2025-05-30 12:57:26.804 | INFO     | __main__:create_deployment:776 - ✅ Successfully created deployment:
2025-05-30 12:57:26.804 | INFO     | __main__:create_deployment:777 -    Name: ps-dev-ca-tstar
2025-05-30 12:57:26.804 | INFO     | __main__:create_deployment:778 -    Endpoint: ps-dev-ca-tstar
2025-05-30 12:57:26.804 | INFO     | __main__:create_deployment:779 -    Status: Succeeded
2025-05-30 12:57:26.805 | INFO     | __main__:create_deployment:780 - ========================================
2025-05-30 12:57:26.805 | INFO     | __main__:deploy_with_retry:870 - 🚦 STEP 5/5: Traffic Configuration
2025-05-30 12:57:26.805 | INFO     | __main__:configure_traffic:809 - Configuring traffic for deployment: ps-dev-ca-tstar
2025-05-30 12:57:27.736 | INFO     | __main__:_wait_for_operation_completion:389 - Starting traffic configuration operation (timeout: 1800s)
2025-05-30 12:57:27.740 | INFO     | __main__:_wait_for_operation_completion:402 - traffic configuration in progress... (0s elapsed)
2025-05-30 12:57:57.770 | INFO     | __main__:_wait_for_operation_completion:402 - traffic configuration in progress... (30s elapsed)
2025-05-30 12:58:02.776 | INFO     | __main__:_wait_for_operation_completion:408 - traffic configuration completed successfully in 35.0 seconds
2025-05-30 12:58:03.057 | INFO     | __main__:configure_traffic:824 - Traffic configuration completed: {'ps-dev-ca-tstar': 100}
2025-05-30 12:58:03.057 | INFO     | __main__:deploy_with_retry:873 - ✅ All deployment steps completed successfully!
2025-05-30 12:58:03.057 | INFO     | __main__:deploy_with_retry:874 - ==================================================
2025-05-30 12:58:03.058 | INFO     | __main__:main:1003 - 🎉 DEPLOYMENT COMPLETED SUCCESSFULLY!
2025-05-30 12:58:03.058 | INFO     | __main__:main:1004 - ============================================================
2025-05-30 12:58:03.058 | INFO     | __main__:main:1005 - FINAL DEPLOYMENT SUMMARY
2025-05-30 12:58:03.058 | INFO     | __main__:main:1006 - ============================================================
2025-05-30 12:58:03.058 | INFO     | __main__:main:1007 - ✅ Endpoint: ps-dev-ca-tstar
2025-05-30 12:58:03.058 | INFO     | __main__:main:1008 - ✅ Deployment: ps-dev-ca-tstar
2025-05-30 12:58:03.058 | INFO     | __main__:main:1009 - ✅ Traffic Configuration: {'ps-dev-ca-tstar': 100}
2025-05-30 12:58:03.058 | INFO     | __main__:main:1010 - ✅ Instance Type: STANDARD_NC4AS_T4_V3
2025-05-30 12:58:03.058 | INFO     | __main__:main:1011 - ✅ Instance Count: 1
2025-05-30 12:58:03.058 | INFO     | __main__:main:1012 - ✅ Workspace: ps-dev-mlw-claimsauto
2025-05-30 12:58:03.058 | INFO     | __main__:main:1013 - ✅ Resource Group: ps-dev-rg-claimsautoml
2025-05-30 12:58:03.058 | INFO     | __main__:main:1014 - ============================================================
2025-05-30 12:58:03.058 | INFO     | __main__:main:1017 - 📋 NEXT STEPS:
2025-05-30 12:58:03.058 | INFO     | __main__:main:1018 -    1. Test the deployed endpoint with sample data
2025-05-30 12:58:03.059 | INFO     | __main__:main:1019 -    2. Monitor deployment performance and logs
2025-05-30 12:58:03.059 | INFO     | __main__:main:1020 -    3. Update traffic allocation if needed
2025-05-30 12:58:03.059 | INFO     | __main__:main:1021 -    4. Set up monitoring and alerting
2025-05-30 12:58:03.059 | INFO     | __main__:main:1022 - ============================================================
2025-05-30 12:58:03.059 | SUCCESS  | __main__:main:1024 - 🎉 Deployment completed successfully!
2025-05-30 12:58:03.059 | INFO     | __main__:deployment_context:957 - Deployment context completed successfully
2025-05-30 12:58:03.059 | INFO     | __main__:deployment_context:962 - Deployment context cleanup completed
