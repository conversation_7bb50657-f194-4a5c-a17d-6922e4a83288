2025-05-30 00:18:03.135 | INFO     | __main__:setup_logging:98 - Logging configured - Log file: logs/deployment_2025-05-30_00-18-03.log
2025-05-30 00:18:03.136 | INFO     | __main__:deployment_context:677 - Starting deployment context
2025-05-30 00:18:03.138 | INFO     | __main__:load_and_validate_environment:665 - Environment configuration loaded and validated successfully
2025-05-30 00:18:03.138 | INFO     | __main__:__init__:184 - Initialized AzureMLDeployer with configuration
2025-05-30 00:18:03.138 | INFO     | __main__:deploy_with_retry:582 - Starting deployment attempt 1/1
2025-05-30 00:18:03.138 | INFO     | __main__:register_model:302 - Checking for existing model: ps-dev-ca-tstarc
2025-05-30 00:18:03.138 | INFO     | __main__:_create_ml_client:204 - Creating Azure ML client connection
2025-05-30 00:18:03.829 | INFO     | __main__:_create_ml_client:219 - Successfully connected to workspace: t-to-tstar
2025-05-30 00:18:04.323 | INFO     | __main__:register_model:307 - Found existing model: ps-dev-ca-tstarc (version: 1)
2025-05-30 00:18:04.323 | INFO     | __main__:create_environment:348 - Checking for existing environment: ps-dev-ca-tstarc
2025-05-30 00:18:05.308 | INFO     | __main__:create_environment:353 - Found existing environment: ps-dev-ca-tstarc (version: 6)
2025-05-30 00:18:05.308 | INFO     | __main__:create_endpoint:394 - Checking for existing endpoint: ps-dev-ca-tstarc
2025-05-30 00:18:05.764 | INFO     | __main__:create_endpoint:399 - Found existing endpoint: ps-dev-ca-tstarc
2025-05-30 00:18:05.765 | INFO     | __main__:create_deployment:477 - Creating deployment: ps-dev-ca-tstarc
2025-05-30 00:18:05.765 | INFO     | __main__:create_deployment:478 - Using model: ps-dev-ca-tstarc (version: 1)
2025-05-30 00:18:05.765 | INFO     | __main__:create_deployment:479 - Using environment: ps-dev-ca-tstarc (version: 6)
2025-05-30 00:18:05.765 | INFO     | __main__:create_deployment:480 - Using code path: /home/<USER>/repos/autolodge_retrained_deploy/Python
2025-05-30 00:18:05.765 | INFO     | __main__:create_deployment:503 - Starting deployment creation...
2025-05-30 00:18:13.674 | INFO     | __main__:_wait_for_operation_completion:263 - Starting deployment creation operation (timeout: 1800s)
2025-05-30 00:18:13.675 | INFO     | __main__:_wait_for_operation_completion:276 - deployment creation in progress... (0s elapsed)
2025-05-30 00:18:43.697 | INFO     | __main__:_wait_for_operation_completion:276 - deployment creation in progress... (30s elapsed)
2025-05-30 00:19:13.724 | INFO     | __main__:_wait_for_operation_completion:276 - deployment creation in progress... (60s elapsed)
2025-05-30 00:19:43.751 | INFO     | __main__:_wait_for_operation_completion:276 - deployment creation in progress... (90s elapsed)
2025-05-30 00:20:13.780 | INFO     | __main__:_wait_for_operation_completion:276 - deployment creation in progress... (120s elapsed)
2025-05-30 00:20:43.807 | INFO     | __main__:_wait_for_operation_completion:276 - deployment creation in progress... (150s elapsed)
2025-05-30 00:21:13.833 | INFO     | __main__:_wait_for_operation_completion:276 - deployment creation in progress... (180s elapsed)
2025-05-30 00:21:43.862 | INFO     | __main__:_wait_for_operation_completion:276 - deployment creation in progress... (210s elapsed)
2025-05-30 00:22:13.887 | INFO     | __main__:_wait_for_operation_completion:276 - deployment creation in progress... (240s elapsed)
2025-05-30 00:22:43.910 | INFO     | __main__:_wait_for_operation_completion:276 - deployment creation in progress... (270s elapsed)
2025-05-30 00:23:13.940 | INFO     | __main__:_wait_for_operation_completion:276 - deployment creation in progress... (300s elapsed)
2025-05-30 00:23:43.964 | INFO     | __main__:_wait_for_operation_completion:276 - deployment creation in progress... (330s elapsed)
2025-05-30 00:24:13.993 | ERROR    | __main__:_wait_for_operation_completion:288 - deployment creation operation failed: (ResourceNotReady) User container has crashed or terminated. Please see troubleshooting guide, available here: https://aka.ms/oe-tsg#error-resourcenotready
Code: ResourceNotReady
Message: User container has crashed or terminated. Please see troubleshooting guide, available here: https://aka.ms/oe-tsg#error-resourcenotready
2025-05-30 00:24:13.994 | ERROR    | __main__:create_deployment:528 - Failed to create deployment ps-dev-ca-tstarc: deployment creation operation failed: (ResourceNotReady) User container has crashed or terminated. Please see troubleshooting guide, available here: https://aka.ms/oe-tsg#error-resourcenotready
Code: ResourceNotReady
Message: User container has crashed or terminated. Please see troubleshooting guide, available here: https://aka.ms/oe-tsg#error-resourcenotready
2025-05-30 00:24:13.994 | ERROR    | __main__:deploy_with_retry:604 - Deployment attempt 1 failed: Failed to create deployment ps-dev-ca-tstarc: deployment creation operation failed: (ResourceNotReady) User container has crashed or terminated. Please see troubleshooting guide, available here: https://aka.ms/oe-tsg#error-resourcenotready
Code: ResourceNotReady
Message: User container has crashed or terminated. Please see troubleshooting guide, available here: https://aka.ms/oe-tsg#error-resourcenotready
2025-05-30 00:24:13.994 | ERROR    | __main__:deploy_with_retry:610 - All deployment attempts failed
2025-05-30 00:24:13.994 | ERROR    | __main__:deployment_context:682 - Deployment context failed: Deployment failed after 1 attempts. Last error: Failed to create deployment ps-dev-ca-tstarc: deployment creation operation failed: (ResourceNotReady) User container has crashed or terminated. Please see troubleshooting guide, available here: https://aka.ms/oe-tsg#error-resourcenotready
Code: ResourceNotReady
Message: User container has crashed or terminated. Please see troubleshooting guide, available here: https://aka.ms/oe-tsg#error-resourcenotready
2025-05-30 00:24:13.994 | INFO     | __main__:deployment_context:685 - Deployment context cleanup completed
2025-05-30 00:24:13.994 | ERROR    | __main__:main:720 - Deployment failed: Deployment failed after 1 attempts. Last error: Failed to create deployment ps-dev-ca-tstarc: deployment creation operation failed: (ResourceNotReady) User container has crashed or terminated. Please see troubleshooting guide, available here: https://aka.ms/oe-tsg#error-resourcenotready
Code: ResourceNotReady
Message: User container has crashed or terminated. Please see troubleshooting guide, available here: https://aka.ms/oe-tsg#error-resourcenotready
