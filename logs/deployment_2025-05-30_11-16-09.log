2025-05-30 11:16:09.016 | INFO     | __main__:setup_logging:98 - Logging configured - Log file: logs/deployment_2025-05-30_11-16-09.log
2025-05-30 11:16:09.017 | INFO     | __main__:deployment_context:917 - Starting deployment context
2025-05-30 11:16:09.017 | INFO     | __main__:main:937 - 🚀 AZURE ML DEPLOYMENT SCRIPT STARTED
2025-05-30 11:16:09.017 | INFO     | __main__:main:938 - ============================================================
2025-05-30 11:16:09.017 | INFO     | __main__:main:941 - 📋 STEP 1: Loading and validating configuration...
2025-05-30 11:16:09.019 | INFO     | __main__:load_and_validate_environment:905 - Environment configuration loaded and validated successfully
2025-05-30 11:16:09.019 | INFO     | __main__:main:943 - ✅ Configuration loaded successfully
2025-05-30 11:16:09.019 | INFO     | __main__:main:946 - 📊 STEP 2: Displaying deployment summary...
2025-05-30 11:16:09.019 | INFO     | __main__:display_deployment_summary:176 - ================================================================================
2025-05-30 11:16:09.019 | INFO     | __main__:display_deployment_summary:177 - DEPLOYMENT CONFIGURATION SUMMARY
2025-05-30 11:16:09.019 | INFO     | __main__:display_deployment_summary:178 - ================================================================================
2025-05-30 11:16:09.019 | INFO     | __main__:display_deployment_summary:181 - 🔧 Azure Environment:
2025-05-30 11:16:09.019 | INFO     | __main__:display_deployment_summary:182 -    Subscription ID: b15ae5d0-8f07-4cfb-aca3-508d38e9d983
2025-05-30 11:16:09.019 | INFO     | __main__:display_deployment_summary:183 -    Resource Group:  ps-sit-rg-claimsautoml
2025-05-30 11:16:09.019 | INFO     | __main__:display_deployment_summary:184 -    Workspace:       ps-sit-mlw-claimsauto
2025-05-30 11:16:09.020 | INFO     | __main__:display_deployment_summary:187 - 🚀 Deployment Configuration:
2025-05-30 11:16:09.020 | INFO     | __main__:display_deployment_summary:188 -    Model Name:      ps-sit-ca-tstar
2025-05-30 11:16:09.020 | INFO     | __main__:display_deployment_summary:189 -    Environment:     ps-sit-ca-tstar
2025-05-30 11:16:09.020 | INFO     | __main__:display_deployment_summary:190 -    Endpoint Name:   ps-sit-ca-tstar
2025-05-30 11:16:09.020 | INFO     | __main__:display_deployment_summary:191 -    Deployment Name: ps-sit-ca-tstar
2025-05-30 11:16:09.020 | INFO     | __main__:display_deployment_summary:194 - 💻 Infrastructure:
2025-05-30 11:16:09.020 | INFO     | __main__:display_deployment_summary:195 -    Instance Type:   Standard_DS3_v2
2025-05-30 11:16:09.020 | INFO     | __main__:display_deployment_summary:196 -    Instance Count:  1
2025-05-30 11:16:09.020 | INFO     | __main__:display_deployment_summary:197 -    Timeout:         1800 seconds
2025-05-30 11:16:09.020 | INFO     | __main__:display_deployment_summary:198 -    Max Retries:     1
2025-05-30 11:16:09.020 | INFO     | __main__:display_deployment_summary:201 - 📁 File Validation:
2025-05-30 11:16:09.020 | INFO     | __main__:display_deployment_summary:207 -    Model File:      ✅ EXISTS (60.4 MB)
2025-05-30 11:16:09.020 | INFO     | __main__:display_deployment_summary:208 -    Model Path:      /home/<USER>/repos/autolodge_retrained_deploy/data/models/autolodge.h5
2025-05-30 11:16:09.021 | INFO     | __main__:display_deployment_summary:213 -    Conda File:      ✅ EXISTS
2025-05-30 11:16:09.021 | INFO     | __main__:display_deployment_summary:214 -    Conda Path:      /home/<USER>/repos/autolodge_retrained_deploy/configs/autolodge.yaml
2025-05-30 11:16:09.021 | INFO     | __main__:display_deployment_summary:219 -    Scoring Script:  ✅ EXISTS
2025-05-30 11:16:09.021 | INFO     | __main__:display_deployment_summary:220 -    Script Path:     /home/<USER>/repos/autolodge_retrained_deploy/Python/score.py
2025-05-30 11:16:09.021 | INFO     | __main__:display_deployment_summary:223 - 🔐 Environment Variables:
2025-05-30 11:16:09.021 | INFO     | __main__:display_deployment_summary:237 -    AZURE_SUBSCRIPTION_ID: ✅ b15ae5d0...
2025-05-30 11:16:09.021 | INFO     | __main__:display_deployment_summary:237 -    AZURE_RESOURCE_GROUP: ✅ ps-sit-rg-claimsautoml
2025-05-30 11:16:09.021 | INFO     | __main__:display_deployment_summary:237 -    AZURE_ML_WORKSPACE_NAME: ✅ ps-sit-mlw-claimsauto
2025-05-30 11:16:09.021 | INFO     | __main__:display_deployment_summary:237 -    MODEL_NAME: ✅ ps-sit-ca-tstar
2025-05-30 11:16:09.021 | INFO     | __main__:display_deployment_summary:237 -    ENV_NAME: ✅ ps-sit-ca-tstar
2025-05-30 11:16:09.021 | INFO     | __main__:display_deployment_summary:237 -    ENDPOINT_NAME: ✅ ps-sit-ca-tstar
2025-05-30 11:16:09.021 | INFO     | __main__:display_deployment_summary:237 -    DEPLOYMENT_NAME: ✅ ps-sit-ca-tstar
2025-05-30 11:16:09.021 | INFO     | __main__:display_deployment_summary:239 - ================================================================================
2025-05-30 11:16:09.021 | INFO     | __main__:main:950 - 🔐 STEP 3: User confirmation required...
2025-05-30 11:16:09.022 | INFO     | __main__:get_user_confirmation:252 - ⚠️  DEPLOYMENT CONFIRMATION REQUIRED
2025-05-30 11:16:09.022 | INFO     | __main__:get_user_confirmation:253 - ==================================================
2025-05-30 11:16:09.022 | WARNING  | __main__:get_user_confirmation:256 - 🎯 Target Environment: ps-sit-mlw-claimsauto
2025-05-30 11:16:09.022 | WARNING  | __main__:get_user_confirmation:257 - 🚀 Deployment Name: ps-sit-ca-tstar
2025-05-30 11:16:09.022 | WARNING  | __main__:get_user_confirmation:258 - 📦 Model: ps-sit-ca-tstar
2025-05-30 11:16:09.022 | WARNING  | __main__:get_user_confirmation:259 - 💰 Instance Type: Standard_DS3_v2 (Count: 1)
2025-05-30 11:16:09.022 | INFO     | __main__:get_user_confirmation:262 - ⚠️  Potential Risks:
2025-05-30 11:16:09.022 | INFO     | __main__:get_user_confirmation:263 -    • This will create/update Azure ML resources
2025-05-30 11:16:09.022 | INFO     | __main__:get_user_confirmation:264 -    • Existing deployments with the same name may be overwritten
2025-05-30 11:16:09.022 | INFO     | __main__:get_user_confirmation:265 -    • Azure costs will be incurred for compute resources
2025-05-30 11:16:09.022 | INFO     | __main__:get_user_confirmation:266 -    • The deployment process may take 15-30 minutes
2025-05-30 11:16:09.022 | INFO     | __main__:get_user_confirmation:268 - ==================================================
2025-05-30 11:16:12.626 | INFO     | __main__:get_user_confirmation:277 - ✅ User confirmed deployment. Proceeding...
2025-05-30 11:16:12.626 | INFO     | __main__:main:956 - 🔧 STEP 4: Initializing Azure ML deployer...
2025-05-30 11:16:12.626 | INFO     | __main__:__init__:310 - Initialized AzureMLDeployer with configuration
2025-05-30 11:16:12.626 | INFO     | __main__:main:958 - ✅ Deployer initialized successfully
2025-05-30 11:16:12.626 | INFO     | __main__:main:961 - 🚀 STEP 5: Executing deployment...
2025-05-30 11:16:12.626 | INFO     | __main__:main:962 - ⚠️  WARNING: Irreversible deployment operations will now begin!
2025-05-30 11:16:12.626 | INFO     | __main__:deploy_with_retry:809 - 🔄 DEPLOYMENT EXECUTION PHASE
2025-05-30 11:16:12.626 | INFO     | __main__:deploy_with_retry:810 - ==================================================
2025-05-30 11:16:12.626 | INFO     | __main__:deploy_with_retry:811 - Starting deployment attempt 1/1
2025-05-30 11:16:12.626 | INFO     | __main__:deploy_with_retry:817 - 📦 STEP 1/5: Model Registration
2025-05-30 11:16:12.627 | INFO     | __main__:register_model:428 - 🔍 MODEL REGISTRATION PHASE
2025-05-30 11:16:12.627 | INFO     | __main__:register_model:429 - ========================================
2025-05-30 11:16:12.627 | INFO     | __main__:register_model:430 - Checking for existing model: ps-sit-ca-tstar
2025-05-30 11:16:12.627 | INFO     | __main__:_create_ml_client:330 - Creating Azure ML client connection
2025-05-30 11:16:13.304 | INFO     | __main__:_create_ml_client:345 - Successfully connected to workspace: ps-sit-mlw-claimsauto
2025-05-30 11:16:13.600 | INFO     | __main__:register_model:435 - ✅ Found existing model: ps-sit-ca-tstar (version: 1)
2025-05-30 11:16:13.601 | INFO     | __main__:register_model:436 -    Model ID: /subscriptions/b15ae5d0-8f07-4cfb-aca3-508d38e9d983/resourceGroups/ps-sit-rg-claimsautoml/providers/Microsoft.MachineLearningServices/workspaces/ps-sit-mlw-claimsauto/models/ps-sit-ca-tstar/versions/1
2025-05-30 11:16:13.601 | ERROR    | __main__:register_model:481 - ❌ Failed to register model ps-sit-ca-tstar: 'SystemData' object has no attribute 'get'
2025-05-30 11:16:13.601 | ERROR    | __main__:deploy_with_retry:842 - ❌ Deployment attempt 1 failed: Failed to register model ps-sit-ca-tstar: 'SystemData' object has no attribute 'get'
2025-05-30 11:16:13.601 | ERROR    | __main__:deploy_with_retry:849 - 💥 All deployment attempts failed
2025-05-30 11:16:13.601 | ERROR    | __main__:deploy_with_retry:856 - 💥 Deployment failed after 1 attempts. Last error: Failed to register model ps-sit-ca-tstar: 'SystemData' object has no attribute 'get'
2025-05-30 11:16:13.601 | ERROR    | __main__:deployment_context:922 - Deployment context failed: Deployment failed after 1 attempts. Last error: Failed to register model ps-sit-ca-tstar: 'SystemData' object has no attribute 'get'
2025-05-30 11:16:13.601 | INFO     | __main__:deployment_context:925 - Deployment context cleanup completed
2025-05-30 11:16:13.601 | ERROR    | __main__:main:990 - ❌ Deployment failed: Deployment failed after 1 attempts. Last error: Failed to register model ps-sit-ca-tstar: 'SystemData' object has no attribute 'get'
2025-05-30 11:16:13.601 | ERROR    | __main__:main:991 - 💡 Check the log file for detailed error information
