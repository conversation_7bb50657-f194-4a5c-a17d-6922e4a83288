2025-05-30 00:59:46.072 | INFO     | __main__:setup_logging:98 - Logging configured - Log file: logs/deployment_2025-05-30_00-59-46.log
2025-05-30 00:59:46.073 | INFO     | __main__:deployment_context:677 - Starting deployment context
2025-05-30 00:59:46.075 | INFO     | __main__:load_and_validate_environment:665 - Environment configuration loaded and validated successfully
2025-05-30 00:59:46.075 | INFO     | __main__:__init__:184 - Initialized AzureMLDeployer with configuration
2025-05-30 00:59:46.075 | INFO     | __main__:deploy_with_retry:582 - Starting deployment attempt 1/1
2025-05-30 00:59:46.075 | INFO     | __main__:register_model:302 - Checking for existing model: ps-dev-ca-tstarc
2025-05-30 00:59:46.075 | INFO     | __main__:_create_ml_client:204 - Creating Azure ML client connection
2025-05-30 00:59:46.730 | INFO     | __main__:_create_ml_client:219 - Successfully connected to workspace: t-to-tstar
2025-05-30 00:59:47.151 | INFO     | __main__:register_model:307 - Found existing model: ps-dev-ca-tstarc (version: 1)
2025-05-30 00:59:47.151 | INFO     | __main__:create_environment:348 - Checking for existing environment: ps-dev-ca-tstarc
2025-05-30 00:59:51.579 | INFO     | __main__:create_environment:353 - Found existing environment: ps-dev-ca-tstarc (version: 7)
2025-05-30 00:59:51.579 | INFO     | __main__:create_endpoint:394 - Checking for existing endpoint: ps-dev-ca-tstarc
2025-05-30 00:59:51.998 | INFO     | __main__:create_endpoint:399 - Found existing endpoint: ps-dev-ca-tstarc
2025-05-30 00:59:51.998 | INFO     | __main__:create_deployment:477 - Creating deployment: ps-dev-ca-tstarc
2025-05-30 00:59:51.998 | INFO     | __main__:create_deployment:478 - Using model: ps-dev-ca-tstarc (version: 1)
2025-05-30 00:59:51.998 | INFO     | __main__:create_deployment:479 - Using environment: ps-dev-ca-tstarc (version: 7)
2025-05-30 00:59:51.998 | INFO     | __main__:create_deployment:480 - Using code path: /home/<USER>/repos/autolodge_retrained_deploy/Python
2025-05-30 00:59:51.998 | INFO     | __main__:create_deployment:503 - Starting deployment creation...
2025-05-30 00:59:58.756 | INFO     | __main__:_wait_for_operation_completion:263 - Starting deployment creation operation (timeout: 1800s)
2025-05-30 00:59:58.757 | INFO     | __main__:_wait_for_operation_completion:276 - deployment creation in progress... (0s elapsed)
2025-05-30 01:00:28.786 | INFO     | __main__:_wait_for_operation_completion:276 - deployment creation in progress... (30s elapsed)
2025-05-30 01:00:58.811 | INFO     | __main__:_wait_for_operation_completion:276 - deployment creation in progress... (60s elapsed)
2025-05-30 01:01:28.840 | INFO     | __main__:_wait_for_operation_completion:276 - deployment creation in progress... (90s elapsed)
2025-05-30 01:01:58.865 | INFO     | __main__:_wait_for_operation_completion:276 - deployment creation in progress... (120s elapsed)
2025-05-30 01:02:28.891 | INFO     | __main__:_wait_for_operation_completion:276 - deployment creation in progress... (150s elapsed)
2025-05-30 01:02:58.917 | INFO     | __main__:_wait_for_operation_completion:276 - deployment creation in progress... (180s elapsed)
2025-05-30 01:03:28.939 | INFO     | __main__:_wait_for_operation_completion:276 - deployment creation in progress... (210s elapsed)
2025-05-30 01:03:58.964 | INFO     | __main__:_wait_for_operation_completion:276 - deployment creation in progress... (240s elapsed)
2025-05-30 01:04:28.990 | INFO     | __main__:_wait_for_operation_completion:276 - deployment creation in progress... (270s elapsed)
2025-05-30 01:04:59.017 | INFO     | __main__:_wait_for_operation_completion:276 - deployment creation in progress... (300s elapsed)
2025-05-30 01:05:29.042 | INFO     | __main__:_wait_for_operation_completion:276 - deployment creation in progress... (330s elapsed)
2025-05-30 01:05:59.070 | INFO     | __main__:_wait_for_operation_completion:276 - deployment creation in progress... (360s elapsed)
2025-05-30 01:06:29.100 | INFO     | __main__:_wait_for_operation_completion:276 - deployment creation in progress... (390s elapsed)
2025-05-30 01:06:59.125 | INFO     | __main__:_wait_for_operation_completion:276 - deployment creation in progress... (420s elapsed)
2025-05-30 01:07:24.147 | INFO     | __main__:_wait_for_operation_completion:282 - deployment creation completed successfully in 445.4 seconds
2025-05-30 01:07:24.234 | INFO     | __main__:create_deployment:516 - Successfully created deployment: ps-dev-ca-tstarc
2025-05-30 01:07:24.234 | INFO     | __main__:configure_traffic:545 - Configuring traffic for deployment: ps-dev-ca-tstarc
2025-05-30 01:07:25.338 | INFO     | __main__:_wait_for_operation_completion:263 - Starting traffic configuration operation (timeout: 1800s)
2025-05-30 01:07:25.338 | INFO     | __main__:_wait_for_operation_completion:276 - traffic configuration in progress... (0s elapsed)
2025-05-30 01:07:55.362 | INFO     | __main__:_wait_for_operation_completion:276 - traffic configuration in progress... (30s elapsed)
2025-05-30 01:08:00.368 | INFO     | __main__:_wait_for_operation_completion:282 - traffic configuration completed successfully in 35.0 seconds
2025-05-30 01:08:00.552 | INFO     | __main__:configure_traffic:560 - Traffic configuration completed: {'ps-dev-ca-tstarc': 100}
2025-05-30 01:08:00.552 | INFO     | __main__:deploy_with_retry:599 - Deployment completed successfully!
2025-05-30 01:08:00.553 | INFO     | __main__:main:707 - ============================================================
2025-05-30 01:08:00.553 | INFO     | __main__:main:708 - DEPLOYMENT SUMMARY
2025-05-30 01:08:00.553 | INFO     | __main__:main:709 - ============================================================
2025-05-30 01:08:00.553 | INFO     | __main__:main:710 - Endpoint: ps-dev-ca-tstarc
2025-05-30 01:08:00.553 | INFO     | __main__:main:711 - Deployment: ps-dev-ca-tstarc
2025-05-30 01:08:00.553 | INFO     | __main__:main:712 - Traffic Configuration: {'ps-dev-ca-tstarc': 100}
2025-05-30 01:08:00.553 | INFO     | __main__:main:713 - Instance Type: Standard_DS3_v2
2025-05-30 01:08:00.553 | INFO     | __main__:main:714 - Instance Count: 1
2025-05-30 01:08:00.553 | INFO     | __main__:main:715 - ============================================================
2025-05-30 01:08:00.553 | INFO     | __main__:main:717 - Deployment completed successfully!
2025-05-30 01:08:00.553 | INFO     | __main__:deployment_context:680 - Deployment context completed successfully
2025-05-30 01:08:00.553 | INFO     | __main__:deployment_context:685 - Deployment context cleanup completed
