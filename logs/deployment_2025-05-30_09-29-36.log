2025-05-30 09:29:36.097 | INFO     | __main__:setup_logging:98 - Logging configured - Log file: logs/deployment_2025-05-30_09-29-36.log
2025-05-30 09:29:36.098 | INFO     | __main__:deployment_context:677 - Starting deployment context
2025-05-30 09:29:36.100 | INFO     | __main__:load_and_validate_environment:665 - Environment configuration loaded and validated successfully
2025-05-30 09:29:36.100 | INFO     | __main__:__init__:184 - Initialized AzureMLDeployer with configuration
2025-05-30 09:29:36.100 | INFO     | __main__:deploy_with_retry:582 - Starting deployment attempt 1/1
2025-05-30 09:29:36.100 | INFO     | __main__:register_model:302 - Checking for existing model: ps-dev-ca-tstarc
2025-05-30 09:29:36.100 | INFO     | __main__:_create_ml_client:204 - Creating Azure ML client connection
2025-05-30 09:29:36.739 | INFO     | __main__:_create_ml_client:219 - Successfully connected to workspace: t-to-tstar
2025-05-30 09:29:37.037 | INFO     | __main__:register_model:307 - Found existing model: ps-dev-ca-tstarc (version: 1)
2025-05-30 09:29:37.037 | INFO     | __main__:create_environment:348 - Checking for existing environment: ps-dev-ca-tstarc
2025-05-30 09:29:38.125 | INFO     | __main__:create_environment:353 - Found existing environment: ps-dev-ca-tstarc (version: 7)
2025-05-30 09:29:38.125 | INFO     | __main__:create_endpoint:394 - Checking for existing endpoint: ps-dev-ca-tstarc
2025-05-30 09:29:38.302 | INFO     | __main__:create_endpoint:399 - Found existing endpoint: ps-dev-ca-tstarc
2025-05-30 09:29:38.302 | INFO     | __main__:create_deployment:477 - Creating deployment: ps-dev-ca-tstarc
2025-05-30 09:29:38.303 | INFO     | __main__:create_deployment:478 - Using model: ps-dev-ca-tstarc (version: 1)
2025-05-30 09:29:38.303 | INFO     | __main__:create_deployment:479 - Using environment: ps-dev-ca-tstarc (version: 7)
2025-05-30 09:29:38.303 | INFO     | __main__:create_deployment:480 - Using code path: /home/<USER>/repos/autolodge_retrained_deploy/Python
2025-05-30 09:29:38.303 | INFO     | __main__:create_deployment:503 - Starting deployment creation...
2025-05-30 09:29:42.734 | INFO     | __main__:_wait_for_operation_completion:263 - Starting deployment creation operation (timeout: 1800s)
2025-05-30 09:29:42.735 | INFO     | __main__:_wait_for_operation_completion:276 - deployment creation in progress... (0s elapsed)
2025-05-30 09:29:47.550 | INFO     | __main__:deployment_context:685 - Deployment context cleanup completed
2025-05-30 09:29:47.551 | WARNING  | __main__:main:723 - Deployment interrupted by user
