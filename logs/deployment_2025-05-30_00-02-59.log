2025-05-30 00:02:59.708 | INFO     | __main__:setup_logging:98 - Logging configured - Log file: logs/deployment_2025-05-30_00-02-59.log
2025-05-30 00:02:59.708 | INFO     | __main__:deployment_context:677 - Starting deployment context
2025-05-30 00:02:59.710 | INFO     | __main__:load_and_validate_environment:665 - Environment configuration loaded and validated successfully
2025-05-30 00:02:59.710 | INFO     | __main__:__init__:184 - Initialized AzureMLDeployer with configuration
2025-05-30 00:02:59.710 | INFO     | __main__:deploy_with_retry:582 - Starting deployment attempt 1/1
2025-05-30 00:02:59.710 | INFO     | __main__:register_model:302 - Checking for existing model: ps-dev-ca-tstarc
2025-05-30 00:02:59.711 | INFO     | __main__:_create_ml_client:204 - Creating Azure ML client connection
2025-05-30 00:03:01.022 | INFO     | __main__:_create_ml_client:219 - Successfully connected to workspace: t-to-tstar
2025-05-30 00:03:01.495 | INFO     | __main__:register_model:307 - Found existing model: ps-dev-ca-tstarc (version: 1)
2025-05-30 00:03:01.496 | INFO     | __main__:create_environment:348 - Checking for existing environment: ps-dev-ca-tstarc
2025-05-30 00:03:02.403 | INFO     | __main__:create_environment:353 - Found existing environment: ps-dev-ca-tstarc (version: 6)
2025-05-30 00:03:02.403 | INFO     | __main__:create_endpoint:394 - Checking for existing endpoint: ps-dev-ca-tstarc
2025-05-30 00:03:02.575 | INFO     | __main__:create_endpoint:399 - Found existing endpoint: ps-dev-ca-tstarc
2025-05-30 00:03:02.575 | INFO     | __main__:create_deployment:477 - Creating deployment: ps-dev-ca-tstarc
2025-05-30 00:03:02.576 | INFO     | __main__:create_deployment:478 - Using model: ps-dev-ca-tstarc (version: 1)
2025-05-30 00:03:02.576 | INFO     | __main__:create_deployment:479 - Using environment: ps-dev-ca-tstarc (version: 6)
2025-05-30 00:03:02.576 | INFO     | __main__:create_deployment:480 - Using code path: /home/<USER>/repos/autolodge_retrained_deploy/Python
2025-05-30 00:03:02.576 | INFO     | __main__:create_deployment:503 - Starting deployment creation...
2025-05-30 00:03:12.043 | INFO     | __main__:_wait_for_operation_completion:263 - Starting deployment creation operation (timeout: 1800s)
2025-05-30 00:03:12.044 | INFO     | __main__:_wait_for_operation_completion:276 - deployment creation in progress... (0s elapsed)
2025-05-30 00:03:42.066 | INFO     | __main__:_wait_for_operation_completion:276 - deployment creation in progress... (30s elapsed)
2025-05-30 00:04:12.089 | INFO     | __main__:_wait_for_operation_completion:276 - deployment creation in progress... (60s elapsed)
2025-05-30 00:04:42.119 | INFO     | __main__:_wait_for_operation_completion:276 - deployment creation in progress... (90s elapsed)
2025-05-30 00:05:12.145 | INFO     | __main__:_wait_for_operation_completion:276 - deployment creation in progress... (120s elapsed)
2025-05-30 00:05:42.175 | INFO     | __main__:_wait_for_operation_completion:276 - deployment creation in progress... (150s elapsed)
2025-05-30 00:06:12.200 | INFO     | __main__:_wait_for_operation_completion:276 - deployment creation in progress... (180s elapsed)
2025-05-30 00:06:42.226 | INFO     | __main__:_wait_for_operation_completion:276 - deployment creation in progress... (210s elapsed)
2025-05-30 00:07:12.249 | INFO     | __main__:_wait_for_operation_completion:276 - deployment creation in progress... (240s elapsed)
2025-05-30 00:07:42.274 | INFO     | __main__:_wait_for_operation_completion:276 - deployment creation in progress... (270s elapsed)
2025-05-30 00:08:12.299 | INFO     | __main__:_wait_for_operation_completion:276 - deployment creation in progress... (300s elapsed)
2025-05-30 00:08:42.324 | INFO     | __main__:_wait_for_operation_completion:276 - deployment creation in progress... (330s elapsed)
2025-05-30 00:09:12.354 | INFO     | __main__:_wait_for_operation_completion:276 - deployment creation in progress... (360s elapsed)
2025-05-30 00:09:17.359 | ERROR    | __main__:_wait_for_operation_completion:288 - deployment creation operation failed: (ResourceNotReady) User container has crashed or terminated. Please see troubleshooting guide, available here: https://aka.ms/oe-tsg#error-resourcenotready
Code: ResourceNotReady
Message: User container has crashed or terminated. Please see troubleshooting guide, available here: https://aka.ms/oe-tsg#error-resourcenotready
2025-05-30 00:09:17.359 | ERROR    | __main__:create_deployment:528 - Failed to create deployment ps-dev-ca-tstarc: deployment creation operation failed: (ResourceNotReady) User container has crashed or terminated. Please see troubleshooting guide, available here: https://aka.ms/oe-tsg#error-resourcenotready
Code: ResourceNotReady
Message: User container has crashed or terminated. Please see troubleshooting guide, available here: https://aka.ms/oe-tsg#error-resourcenotready
2025-05-30 00:09:17.359 | ERROR    | __main__:deploy_with_retry:604 - Deployment attempt 1 failed: Failed to create deployment ps-dev-ca-tstarc: deployment creation operation failed: (ResourceNotReady) User container has crashed or terminated. Please see troubleshooting guide, available here: https://aka.ms/oe-tsg#error-resourcenotready
Code: ResourceNotReady
Message: User container has crashed or terminated. Please see troubleshooting guide, available here: https://aka.ms/oe-tsg#error-resourcenotready
2025-05-30 00:09:17.359 | ERROR    | __main__:deploy_with_retry:610 - All deployment attempts failed
2025-05-30 00:09:17.360 | ERROR    | __main__:deployment_context:682 - Deployment context failed: Deployment failed after 1 attempts. Last error: Failed to create deployment ps-dev-ca-tstarc: deployment creation operation failed: (ResourceNotReady) User container has crashed or terminated. Please see troubleshooting guide, available here: https://aka.ms/oe-tsg#error-resourcenotready
Code: ResourceNotReady
Message: User container has crashed or terminated. Please see troubleshooting guide, available here: https://aka.ms/oe-tsg#error-resourcenotready
2025-05-30 00:09:17.360 | INFO     | __main__:deployment_context:685 - Deployment context cleanup completed
2025-05-30 00:09:17.360 | ERROR    | __main__:main:720 - Deployment failed: Deployment failed after 1 attempts. Last error: Failed to create deployment ps-dev-ca-tstarc: deployment creation operation failed: (ResourceNotReady) User container has crashed or terminated. Please see troubleshooting guide, available here: https://aka.ms/oe-tsg#error-resourcenotready
Code: ResourceNotReady
Message: User container has crashed or terminated. Please see troubleshooting guide, available here: https://aka.ms/oe-tsg#error-resourcenotready
