2025-05-30 09:56:31.564 | INFO     | __main__:setup_logging:98 - Logging configured - Log file: logs/deployment_2025-05-30_09-56-31.log
2025-05-30 09:56:31.565 | INFO     | __main__:deployment_context:917 - Starting deployment context
2025-05-30 09:56:31.565 | INFO     | __main__:main:937 - 🚀 AZURE ML DEPLOYMENT SCRIPT STARTED
2025-05-30 09:56:31.565 | INFO     | __main__:main:938 - ============================================================
2025-05-30 09:56:31.565 | INFO     | __main__:main:941 - 📋 STEP 1: Loading and validating configuration...
2025-05-30 09:56:31.567 | INFO     | __main__:load_and_validate_environment:905 - Environment configuration loaded and validated successfully
2025-05-30 09:56:31.567 | INFO     | __main__:main:943 - ✅ Configuration loaded successfully
2025-05-30 09:56:31.567 | INFO     | __main__:main:946 - 📊 STEP 2: Displaying deployment summary...
2025-05-30 09:56:31.567 | INFO     | __main__:display_deployment_summary:176 - ================================================================================
2025-05-30 09:56:31.567 | INFO     | __main__:display_deployment_summary:177 - DEPLOYMENT CONFIGURATION SUMMARY
2025-05-30 09:56:31.567 | INFO     | __main__:display_deployment_summary:178 - ================================================================================
2025-05-30 09:56:31.567 | INFO     | __main__:display_deployment_summary:181 - 🔧 Azure Environment:
2025-05-30 09:56:31.567 | INFO     | __main__:display_deployment_summary:182 -    Subscription ID: b15ae5d0-8f07-4cfb-aca3-508d38e9d983
2025-05-30 09:56:31.567 | INFO     | __main__:display_deployment_summary:183 -    Resource Group:  ps-uat-rg-claimsautoml
2025-05-30 09:56:31.567 | INFO     | __main__:display_deployment_summary:184 -    Workspace:       ps-uat-mlw-claimsautoml
2025-05-30 09:56:31.567 | INFO     | __main__:display_deployment_summary:187 - 🚀 Deployment Configuration:
2025-05-30 09:56:31.567 | INFO     | __main__:display_deployment_summary:188 -    Model Name:      ps-uat-ca-tstar
2025-05-30 09:56:31.568 | INFO     | __main__:display_deployment_summary:189 -    Environment:     ps-uat-ca-tstar
2025-05-30 09:56:31.568 | INFO     | __main__:display_deployment_summary:190 -    Endpoint Name:   ps-uat-ca-tstar
2025-05-30 09:56:31.568 | INFO     | __main__:display_deployment_summary:191 -    Deployment Name: ps-uat-ca-tstar
2025-05-30 09:56:31.568 | INFO     | __main__:display_deployment_summary:194 - 💻 Infrastructure:
2025-05-30 09:56:31.568 | INFO     | __main__:display_deployment_summary:195 -    Instance Type:   Standard_DS3_v2
2025-05-30 09:56:31.568 | INFO     | __main__:display_deployment_summary:196 -    Instance Count:  1
2025-05-30 09:56:31.568 | INFO     | __main__:display_deployment_summary:197 -    Timeout:         1800 seconds
2025-05-30 09:56:31.568 | INFO     | __main__:display_deployment_summary:198 -    Max Retries:     1
2025-05-30 09:56:31.568 | INFO     | __main__:display_deployment_summary:201 - 📁 File Validation:
2025-05-30 09:56:31.568 | INFO     | __main__:display_deployment_summary:207 -    Model File:      ✅ EXISTS (60.4 MB)
2025-05-30 09:56:31.568 | INFO     | __main__:display_deployment_summary:208 -    Model Path:      /home/<USER>/repos/autolodge_retrained_deploy/data/models/autolodge.h5
2025-05-30 09:56:31.568 | INFO     | __main__:display_deployment_summary:213 -    Conda File:      ✅ EXISTS
2025-05-30 09:56:31.568 | INFO     | __main__:display_deployment_summary:214 -    Conda Path:      /home/<USER>/repos/autolodge_retrained_deploy/configs/ps-dev-claimsauto-tstarc.yaml
2025-05-30 09:56:31.569 | INFO     | __main__:display_deployment_summary:219 -    Scoring Script:  ✅ EXISTS
2025-05-30 09:56:31.569 | INFO     | __main__:display_deployment_summary:220 -    Script Path:     /home/<USER>/repos/autolodge_retrained_deploy/Python/score.py
2025-05-30 09:56:31.569 | INFO     | __main__:display_deployment_summary:223 - 🔐 Environment Variables:
2025-05-30 09:56:31.569 | INFO     | __main__:display_deployment_summary:237 -    AZURE_SUBSCRIPTION_ID: ✅ b15ae5d0...
2025-05-30 09:56:31.569 | INFO     | __main__:display_deployment_summary:237 -    AZURE_RESOURCE_GROUP: ✅ ps-uat-rg-claimsautoml
2025-05-30 09:56:31.569 | INFO     | __main__:display_deployment_summary:237 -    AZURE_ML_WORKSPACE_NAME: ✅ ps-uat-mlw-claimsautoml
2025-05-30 09:56:31.569 | INFO     | __main__:display_deployment_summary:237 -    MODEL_NAME: ✅ ps-uat-ca-tstar
2025-05-30 09:56:31.569 | INFO     | __main__:display_deployment_summary:237 -    ENV_NAME: ✅ ps-uat-ca-tstar
2025-05-30 09:56:31.569 | INFO     | __main__:display_deployment_summary:237 -    ENDPOINT_NAME: ✅ ps-uat-ca-tstar
2025-05-30 09:56:31.569 | INFO     | __main__:display_deployment_summary:237 -    DEPLOYMENT_NAME: ✅ ps-uat-ca-tstar
2025-05-30 09:56:31.569 | INFO     | __main__:display_deployment_summary:239 - ================================================================================
2025-05-30 09:56:31.569 | INFO     | __main__:main:950 - 🔐 STEP 3: User confirmation required...
2025-05-30 09:56:31.569 | INFO     | __main__:get_user_confirmation:252 - ⚠️  DEPLOYMENT CONFIRMATION REQUIRED
2025-05-30 09:56:31.569 | INFO     | __main__:get_user_confirmation:253 - ==================================================
2025-05-30 09:56:31.570 | WARNING  | __main__:get_user_confirmation:256 - 🎯 Target Environment: ps-uat-mlw-claimsautoml
2025-05-30 09:56:31.570 | WARNING  | __main__:get_user_confirmation:257 - 🚀 Deployment Name: ps-uat-ca-tstar
2025-05-30 09:56:31.570 | WARNING  | __main__:get_user_confirmation:258 - 📦 Model: ps-uat-ca-tstar
2025-05-30 09:56:31.570 | WARNING  | __main__:get_user_confirmation:259 - 💰 Instance Type: Standard_DS3_v2 (Count: 1)
2025-05-30 09:56:31.570 | INFO     | __main__:get_user_confirmation:262 - ⚠️  Potential Risks:
2025-05-30 09:56:31.570 | INFO     | __main__:get_user_confirmation:263 -    • This will create/update Azure ML resources
2025-05-30 09:56:31.570 | INFO     | __main__:get_user_confirmation:264 -    • Existing deployments with the same name may be overwritten
2025-05-30 09:56:31.570 | INFO     | __main__:get_user_confirmation:265 -    • Azure costs will be incurred for compute resources
2025-05-30 09:56:31.570 | INFO     | __main__:get_user_confirmation:266 -    • The deployment process may take 15-30 minutes
2025-05-30 09:56:31.570 | INFO     | __main__:get_user_confirmation:268 - ==================================================
2025-05-30 09:56:49.383 | WARNING  | __main__:get_user_confirmation:280 - ❌ User cancelled deployment.
2025-05-30 09:56:49.383 | WARNING  | __main__:main:952 - ❌ Deployment cancelled by user
2025-05-30 09:56:49.383 | INFO     | __main__:deployment_context:925 - Deployment context cleanup completed
