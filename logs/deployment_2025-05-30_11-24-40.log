2025-05-30 11:24:40.959 | INFO     | __main__:setup_logging:98 - Logging configured - Log file: logs/deployment_2025-05-30_11-24-40.log
2025-05-30 11:24:40.960 | INFO     | __main__:deployment_context:932 - Starting deployment context
2025-05-30 11:24:40.961 | INFO     | __main__:main:952 - 🚀 AZURE ML DEPLOYMENT SCRIPT STARTED
2025-05-30 11:24:40.961 | INFO     | __main__:main:953 - ============================================================
2025-05-30 11:24:40.961 | INFO     | __main__:main:956 - 📋 STEP 1: Loading and validating configuration...
2025-05-30 11:24:40.963 | INFO     | __main__:load_and_validate_environment:920 - Environment configuration loaded and validated successfully
2025-05-30 11:24:40.963 | INFO     | __main__:main:958 - ✅ Configuration loaded successfully
2025-05-30 11:24:40.963 | INFO     | __main__:main:961 - 📊 STEP 2: Displaying deployment summary...
2025-05-30 11:24:40.963 | INFO     | __main__:display_deployment_summary:176 - ================================================================================
2025-05-30 11:24:40.963 | INFO     | __main__:display_deployment_summary:177 - DEPLOYMENT CONFIGURATION SUMMARY
2025-05-30 11:24:40.963 | INFO     | __main__:display_deployment_summary:178 - ================================================================================
2025-05-30 11:24:40.964 | INFO     | __main__:display_deployment_summary:181 - 🔧 Azure Environment:
2025-05-30 11:24:40.964 | INFO     | __main__:display_deployment_summary:182 -    Subscription ID: b15ae5d0-8f07-4cfb-aca3-508d38e9d983
2025-05-30 11:24:40.964 | INFO     | __main__:display_deployment_summary:183 -    Resource Group:  ps-sit-rg-claimsautoml
2025-05-30 11:24:40.964 | INFO     | __main__:display_deployment_summary:184 -    Workspace:       ps-sit-mlw-claimsauto
2025-05-30 11:24:40.964 | INFO     | __main__:display_deployment_summary:187 - 🚀 Deployment Configuration:
2025-05-30 11:24:40.964 | INFO     | __main__:display_deployment_summary:188 -    Model Name:      ps-sit-ca-tstar
2025-05-30 11:24:40.964 | INFO     | __main__:display_deployment_summary:189 -    Environment:     ps-sit-ca-tstar
2025-05-30 11:24:40.964 | INFO     | __main__:display_deployment_summary:190 -    Endpoint Name:   ps-sit-ca-tstar
2025-05-30 11:24:40.964 | INFO     | __main__:display_deployment_summary:191 -    Deployment Name: ps-sit-ca-tstar
2025-05-30 11:24:40.964 | INFO     | __main__:display_deployment_summary:194 - 💻 Infrastructure:
2025-05-30 11:24:40.964 | INFO     | __main__:display_deployment_summary:195 -    Instance Type:   Standard_DS3_v2
2025-05-30 11:24:40.964 | INFO     | __main__:display_deployment_summary:196 -    Instance Count:  1
2025-05-30 11:24:40.964 | INFO     | __main__:display_deployment_summary:197 -    Timeout:         1800 seconds
2025-05-30 11:24:40.964 | INFO     | __main__:display_deployment_summary:198 -    Max Retries:     1
2025-05-30 11:24:40.964 | INFO     | __main__:display_deployment_summary:201 - 📁 File Validation:
2025-05-30 11:24:40.965 | INFO     | __main__:display_deployment_summary:207 -    Model File:      ✅ EXISTS (60.4 MB)
2025-05-30 11:24:40.965 | INFO     | __main__:display_deployment_summary:208 -    Model Path:      /home/<USER>/repos/autolodge_retrained_deploy/data/models/autolodge.h5
2025-05-30 11:24:40.965 | INFO     | __main__:display_deployment_summary:213 -    Conda File:      ✅ EXISTS
2025-05-30 11:24:40.965 | INFO     | __main__:display_deployment_summary:214 -    Conda Path:      /home/<USER>/repos/autolodge_retrained_deploy/configs/autolodge.yaml
2025-05-30 11:24:40.965 | INFO     | __main__:display_deployment_summary:219 -    Scoring Script:  ✅ EXISTS
2025-05-30 11:24:40.965 | INFO     | __main__:display_deployment_summary:220 -    Script Path:     /home/<USER>/repos/autolodge_retrained_deploy/Python/score.py
2025-05-30 11:24:40.965 | INFO     | __main__:display_deployment_summary:223 - 🔐 Environment Variables:
2025-05-30 11:24:40.965 | INFO     | __main__:display_deployment_summary:237 -    AZURE_SUBSCRIPTION_ID: ✅ b15ae5d0...
2025-05-30 11:24:40.965 | INFO     | __main__:display_deployment_summary:237 -    AZURE_RESOURCE_GROUP: ✅ ps-sit-rg-claimsautoml
2025-05-30 11:24:40.965 | INFO     | __main__:display_deployment_summary:237 -    AZURE_ML_WORKSPACE_NAME: ✅ ps-sit-mlw-claimsauto
2025-05-30 11:24:40.966 | INFO     | __main__:display_deployment_summary:237 -    MODEL_NAME: ✅ ps-sit-ca-tstar
2025-05-30 11:24:40.966 | INFO     | __main__:display_deployment_summary:237 -    ENV_NAME: ✅ ps-sit-ca-tstar
2025-05-30 11:24:40.966 | INFO     | __main__:display_deployment_summary:237 -    ENDPOINT_NAME: ✅ ps-sit-ca-tstar
2025-05-30 11:24:40.966 | INFO     | __main__:display_deployment_summary:237 -    DEPLOYMENT_NAME: ✅ ps-sit-ca-tstar
2025-05-30 11:24:40.966 | INFO     | __main__:display_deployment_summary:239 - ================================================================================
2025-05-30 11:24:40.966 | INFO     | __main__:main:965 - 🔐 STEP 3: User confirmation required...
2025-05-30 11:24:40.966 | INFO     | __main__:get_user_confirmation:252 - ⚠️  DEPLOYMENT CONFIRMATION REQUIRED
2025-05-30 11:24:40.966 | INFO     | __main__:get_user_confirmation:253 - ==================================================
2025-05-30 11:24:40.966 | WARNING  | __main__:get_user_confirmation:256 - 🎯 Target Environment: ps-sit-mlw-claimsauto
2025-05-30 11:24:40.966 | WARNING  | __main__:get_user_confirmation:257 - 🚀 Deployment Name: ps-sit-ca-tstar
2025-05-30 11:24:40.966 | WARNING  | __main__:get_user_confirmation:258 - 📦 Model: ps-sit-ca-tstar
2025-05-30 11:24:40.966 | WARNING  | __main__:get_user_confirmation:259 - 💰 Instance Type: Standard_DS3_v2 (Count: 1)
2025-05-30 11:24:40.966 | INFO     | __main__:get_user_confirmation:262 - ⚠️  Potential Risks:
2025-05-30 11:24:40.966 | INFO     | __main__:get_user_confirmation:263 -    • This will create/update Azure ML resources
2025-05-30 11:24:40.966 | INFO     | __main__:get_user_confirmation:264 -    • Existing deployments with the same name may be overwritten
2025-05-30 11:24:40.966 | INFO     | __main__:get_user_confirmation:265 -    • Azure costs will be incurred for compute resources
2025-05-30 11:24:40.967 | INFO     | __main__:get_user_confirmation:266 -    • The deployment process may take 15-30 minutes
2025-05-30 11:24:40.967 | INFO     | __main__:get_user_confirmation:268 - ==================================================
2025-05-30 11:24:48.185 | INFO     | __main__:get_user_confirmation:277 - ✅ User confirmed deployment. Proceeding...
2025-05-30 11:24:48.185 | INFO     | __main__:main:971 - 🔧 STEP 4: Initializing Azure ML deployer...
2025-05-30 11:24:48.185 | INFO     | __main__:__init__:310 - Initialized AzureMLDeployer with configuration
2025-05-30 11:24:48.185 | INFO     | __main__:main:973 - ✅ Deployer initialized successfully
2025-05-30 11:24:48.185 | INFO     | __main__:main:976 - 🚀 STEP 5: Executing deployment...
2025-05-30 11:24:48.185 | INFO     | __main__:main:977 - ⚠️  WARNING: Irreversible deployment operations will now begin!
2025-05-30 11:24:48.185 | INFO     | __main__:deploy_with_retry:824 - 🔄 DEPLOYMENT EXECUTION PHASE
2025-05-30 11:24:48.186 | INFO     | __main__:deploy_with_retry:825 - ==================================================
2025-05-30 11:24:48.186 | INFO     | __main__:deploy_with_retry:826 - Starting deployment attempt 1/1
2025-05-30 11:24:48.186 | INFO     | __main__:deploy_with_retry:832 - 📦 STEP 1/5: Model Registration
2025-05-30 11:24:48.186 | INFO     | __main__:register_model:428 - 🔍 MODEL REGISTRATION PHASE
2025-05-30 11:24:48.186 | INFO     | __main__:register_model:429 - ========================================
2025-05-30 11:24:48.186 | INFO     | __main__:register_model:430 - Checking for existing model: ps-sit-ca-tstar
2025-05-30 11:24:48.186 | INFO     | __main__:_create_ml_client:330 - Creating Azure ML client connection
2025-05-30 11:24:48.851 | INFO     | __main__:_create_ml_client:345 - Successfully connected to workspace: ps-sit-mlw-claimsauto
2025-05-30 11:24:49.150 | INFO     | __main__:register_model:435 - ✅ Found existing model: ps-sit-ca-tstar (version: 1)
2025-05-30 11:24:49.150 | INFO     | __main__:register_model:436 -    Model ID: /subscriptions/b15ae5d0-8f07-4cfb-aca3-508d38e9d983/resourceGroups/ps-sit-rg-claimsautoml/providers/Microsoft.MachineLearningServices/workspaces/ps-sit-mlw-claimsauto/models/ps-sit-ca-tstar/versions/1
2025-05-30 11:24:49.150 | DEBUG    | __main__:register_model:440 -    DEBUG: creation_context type: <class 'azure.ai.ml.entities._system_data.SystemData'>
2025-05-30 11:24:49.150 | DEBUG    | __main__:register_model:441 -    DEBUG: creation_context attributes: ['__class__', '__delattr__', '__dict__', '__dir__', '__doc__', '__eq__', '__format__', '__ge__', '__getattribute__', '__gt__', '__hash__', '__init__', '__init_subclass__', '__le__', '__lt__', '__module__', '__ne__', '__new__', '__reduce__', '__reduce_ex__', '__repr__', '__setattr__', '__sizeof__', '__str__', '__subclasshook__', '__weakref__', '_from_rest_object', '_to_dict', '_to_rest_object', 'created_at', 'created_by', 'created_by_type', 'last_modified_at', 'last_modified_by', 'last_modified_by_type']
2025-05-30 11:24:49.151 | INFO     | __main__:register_model:449 -    Created: 2025-05-30 01:15:17.097175+00:00
2025-05-30 11:24:49.151 | INFO     | __main__:register_model:453 -    Description: Model registered on 2025-05-30T11:15:12.245123+10:00
2025-05-30 11:24:49.151 | INFO     | __main__:deploy_with_retry:836 - 🌍 STEP 2/5: Environment Setup
2025-05-30 11:24:49.151 | INFO     | __main__:create_environment:510 - 🌍 ENVIRONMENT SETUP PHASE
2025-05-30 11:24:49.151 | INFO     | __main__:create_environment:511 - ========================================
2025-05-30 11:24:49.151 | INFO     | __main__:create_environment:512 - Checking for existing environment: ps-sit-ca-tstar
2025-05-30 11:24:49.499 | INFO     | __main__:create_environment:524 - ℹ️  Environment ps-sit-ca-tstar not found, creating new one
2025-05-30 11:24:49.499 | INFO     | __main__:create_environment:528 - 📁 Validating conda file: /home/<USER>/repos/autolodge_retrained_deploy/configs/autolodge.yaml
2025-05-30 11:24:49.499 | INFO     | __main__:create_environment:535 - ✅ Conda file validation successful:
2025-05-30 11:24:49.499 | INFO     | __main__:create_environment:536 -    File size: 472 bytes
2025-05-30 11:24:49.499 | INFO     | __main__:create_environment:537 -    Full path: /home/<USER>/repos/autolodge_retrained_deploy/configs/autolodge.yaml
2025-05-30 11:24:49.500 | INFO     | __main__:create_environment:543 -    Conda file preview (first 10 lines):
2025-05-30 11:24:49.500 | INFO     | __main__:create_environment:545 -      1: channels:
2025-05-30 11:24:49.500 | INFO     | __main__:create_environment:545 -      2: - Microsoft
2025-05-30 11:24:49.500 | INFO     | __main__:create_environment:545 -      3: - defaults
2025-05-30 11:24:49.500 | INFO     | __main__:create_environment:545 -      4: dependencies:
2025-05-30 11:24:49.500 | INFO     | __main__:create_environment:545 -      5: - pip
2025-05-30 11:24:49.500 | INFO     | __main__:create_environment:545 -      6: - python=3.9
2025-05-30 11:24:49.500 | INFO     | __main__:create_environment:545 -      7: - pip:
2025-05-30 11:24:49.500 | INFO     | __main__:create_environment:545 -      8: - azure-ai-ml==1.27.1
2025-05-30 11:24:49.500 | INFO     | __main__:create_environment:545 -      9: - azure-identity==1.23.0
2025-05-30 11:24:49.500 | INFO     | __main__:create_environment:545 -      10: - azureml-inference-server-http==1.4.0
2025-05-30 11:24:49.500 | INFO     | __main__:create_environment:547 -      ... (file continues)
2025-05-30 11:24:49.503 | INFO     | __main__:create_environment:560 - 🚀 Creating new environment:
2025-05-30 11:24:49.504 | INFO     | __main__:create_environment:561 -    Environment name: ps-sit-ca-tstar
2025-05-30 11:24:49.504 | INFO     | __main__:create_environment:562 -    Base image: mcr.microsoft.com/azureml/openmpi3.1.2-ubuntu18.04
2025-05-30 11:24:49.504 | INFO     | __main__:create_environment:563 -    Conda file: /home/<USER>/repos/autolodge_retrained_deploy/configs/autolodge.yaml
2025-05-30 11:24:56.715 | INFO     | __main__:create_environment:567 - ✅ Successfully created environment:
2025-05-30 11:24:56.715 | INFO     | __main__:create_environment:568 -    Name: ps-sit-ca-tstar
2025-05-30 11:24:56.715 | INFO     | __main__:create_environment:569 -    Version: 1
2025-05-30 11:24:56.715 | INFO     | __main__:create_environment:570 -    ID: /subscriptions/b15ae5d0-8f07-4cfb-aca3-508d38e9d983/resourceGroups/ps-sit-rg-claimsautoml/providers/Microsoft.MachineLearningServices/workspaces/ps-sit-mlw-claimsauto/environments/ps-sit-ca-tstar/versions/1
2025-05-30 11:24:56.715 | INFO     | __main__:create_environment:571 - ========================================
2025-05-30 11:24:56.715 | INFO     | __main__:deploy_with_retry:840 - 🌐 STEP 3/5: Endpoint Creation
2025-05-30 11:24:56.716 | INFO     | __main__:create_endpoint:591 - Checking for existing endpoint: ps-sit-ca-tstar
2025-05-30 11:24:56.777 | INFO     | __main__:create_endpoint:611 - Endpoint ps-sit-ca-tstar not found, creating new one
2025-05-30 11:24:56.777 | INFO     | __main__:create_endpoint:621 - Creating new endpoint: ps-sit-ca-tstar
2025-05-30 11:24:58.980 | INFO     | __main__:_wait_for_operation_completion:389 - Starting endpoint creation operation (timeout: 1800s)
2025-05-30 11:24:58.984 | INFO     | __main__:_wait_for_operation_completion:402 - endpoint creation in progress... (0s elapsed)
2025-05-30 11:25:29.031 | INFO     | __main__:_wait_for_operation_completion:402 - endpoint creation in progress... (30s elapsed)
2025-05-30 11:25:53.419 | INFO     | __main__:deployment_context:940 - Deployment context cleanup completed
2025-05-30 11:25:53.420 | WARNING  | __main__:main:1009 - ⚠️  Deployment interrupted by user
2025-05-30 11:25:53.420 | INFO     | __main__:main:1010 - 💡 You can safely restart the deployment script
