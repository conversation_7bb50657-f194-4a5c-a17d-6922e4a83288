2025-05-30 09:59:27.910 | INFO     | __main__:setup_logging:98 - Logging configured - Log file: logs/deployment_2025-05-30_09-59-27.log
2025-05-30 09:59:27.912 | INFO     | __main__:deployment_context:917 - Starting deployment context
2025-05-30 09:59:27.912 | INFO     | __main__:main:937 - 🚀 AZURE ML DEPLOYMENT SCRIPT STARTED
2025-05-30 09:59:27.912 | INFO     | __main__:main:938 - ============================================================
2025-05-30 09:59:27.912 | INFO     | __main__:main:941 - 📋 STEP 1: Loading and validating configuration...
2025-05-30 09:59:27.914 | INFO     | __main__:load_and_validate_environment:905 - Environment configuration loaded and validated successfully
2025-05-30 09:59:27.914 | INFO     | __main__:main:943 - ✅ Configuration loaded successfully
2025-05-30 09:59:27.914 | INFO     | __main__:main:946 - 📊 STEP 2: Displaying deployment summary...
2025-05-30 09:59:27.914 | INFO     | __main__:display_deployment_summary:176 - ================================================================================
2025-05-30 09:59:27.914 | INFO     | __main__:display_deployment_summary:177 - DEPLOYMENT CONFIGURATION SUMMARY
2025-05-30 09:59:27.914 | INFO     | __main__:display_deployment_summary:178 - ================================================================================
2025-05-30 09:59:27.914 | INFO     | __main__:display_deployment_summary:181 - 🔧 Azure Environment:
2025-05-30 09:59:27.914 | INFO     | __main__:display_deployment_summary:182 -    Subscription ID: b15ae5d0-8f07-4cfb-aca3-508d38e9d983
2025-05-30 09:59:27.914 | INFO     | __main__:display_deployment_summary:183 -    Resource Group:  ps-uat-rg-claimsautoml
2025-05-30 09:59:27.914 | INFO     | __main__:display_deployment_summary:184 -    Workspace:       ps-uat-mlw-claimsautoml
2025-05-30 09:59:27.914 | INFO     | __main__:display_deployment_summary:187 - 🚀 Deployment Configuration:
2025-05-30 09:59:27.914 | INFO     | __main__:display_deployment_summary:188 -    Model Name:      ps-uat-ca-tstar
2025-05-30 09:59:27.915 | INFO     | __main__:display_deployment_summary:189 -    Environment:     ps-uat-ca-tstar
2025-05-30 09:59:27.915 | INFO     | __main__:display_deployment_summary:190 -    Endpoint Name:   ps-uat-ca-tstar
2025-05-30 09:59:27.915 | INFO     | __main__:display_deployment_summary:191 -    Deployment Name: ps-uat-ca-tstar
2025-05-30 09:59:27.915 | INFO     | __main__:display_deployment_summary:194 - 💻 Infrastructure:
2025-05-30 09:59:27.915 | INFO     | __main__:display_deployment_summary:195 -    Instance Type:   Standard_DS3_v2
2025-05-30 09:59:27.915 | INFO     | __main__:display_deployment_summary:196 -    Instance Count:  1
2025-05-30 09:59:27.915 | INFO     | __main__:display_deployment_summary:197 -    Timeout:         1800 seconds
2025-05-30 09:59:27.915 | INFO     | __main__:display_deployment_summary:198 -    Max Retries:     1
2025-05-30 09:59:27.915 | INFO     | __main__:display_deployment_summary:201 - 📁 File Validation:
2025-05-30 09:59:27.915 | INFO     | __main__:display_deployment_summary:207 -    Model File:      ✅ EXISTS (60.4 MB)
2025-05-30 09:59:27.915 | INFO     | __main__:display_deployment_summary:208 -    Model Path:      /home/<USER>/repos/autolodge_retrained_deploy/data/models/autolodge.h5
2025-05-30 09:59:27.915 | INFO     | __main__:display_deployment_summary:213 -    Conda File:      ✅ EXISTS
2025-05-30 09:59:27.915 | INFO     | __main__:display_deployment_summary:214 -    Conda Path:      /home/<USER>/repos/autolodge_retrained_deploy/configs/ps-uat-claimsauto-tstar.yaml
2025-05-30 09:59:27.916 | INFO     | __main__:display_deployment_summary:219 -    Scoring Script:  ✅ EXISTS
2025-05-30 09:59:27.916 | INFO     | __main__:display_deployment_summary:220 -    Script Path:     /home/<USER>/repos/autolodge_retrained_deploy/Python/score.py
2025-05-30 09:59:27.916 | INFO     | __main__:display_deployment_summary:223 - 🔐 Environment Variables:
2025-05-30 09:59:27.916 | INFO     | __main__:display_deployment_summary:237 -    AZURE_SUBSCRIPTION_ID: ✅ b15ae5d0...
2025-05-30 09:59:27.916 | INFO     | __main__:display_deployment_summary:237 -    AZURE_RESOURCE_GROUP: ✅ ps-uat-rg-claimsautoml
2025-05-30 09:59:27.916 | INFO     | __main__:display_deployment_summary:237 -    AZURE_ML_WORKSPACE_NAME: ✅ ps-uat-mlw-claimsautoml
2025-05-30 09:59:27.916 | INFO     | __main__:display_deployment_summary:237 -    MODEL_NAME: ✅ ps-uat-ca-tstar
2025-05-30 09:59:27.916 | INFO     | __main__:display_deployment_summary:237 -    ENV_NAME: ✅ ps-uat-ca-tstar
2025-05-30 09:59:27.916 | INFO     | __main__:display_deployment_summary:237 -    ENDPOINT_NAME: ✅ ps-uat-ca-tstar
2025-05-30 09:59:27.916 | INFO     | __main__:display_deployment_summary:237 -    DEPLOYMENT_NAME: ✅ ps-uat-ca-tstar
2025-05-30 09:59:27.916 | INFO     | __main__:display_deployment_summary:239 - ================================================================================
2025-05-30 09:59:27.916 | INFO     | __main__:main:950 - 🔐 STEP 3: User confirmation required...
2025-05-30 09:59:27.916 | INFO     | __main__:get_user_confirmation:252 - ⚠️  DEPLOYMENT CONFIRMATION REQUIRED
2025-05-30 09:59:27.916 | INFO     | __main__:get_user_confirmation:253 - ==================================================
2025-05-30 09:59:27.917 | WARNING  | __main__:get_user_confirmation:256 - 🎯 Target Environment: ps-uat-mlw-claimsautoml
2025-05-30 09:59:27.917 | WARNING  | __main__:get_user_confirmation:257 - 🚀 Deployment Name: ps-uat-ca-tstar
2025-05-30 09:59:27.917 | WARNING  | __main__:get_user_confirmation:258 - 📦 Model: ps-uat-ca-tstar
2025-05-30 09:59:27.917 | WARNING  | __main__:get_user_confirmation:259 - 💰 Instance Type: Standard_DS3_v2 (Count: 1)
2025-05-30 09:59:27.917 | INFO     | __main__:get_user_confirmation:262 - ⚠️  Potential Risks:
2025-05-30 09:59:27.917 | INFO     | __main__:get_user_confirmation:263 -    • This will create/update Azure ML resources
2025-05-30 09:59:27.917 | INFO     | __main__:get_user_confirmation:264 -    • Existing deployments with the same name may be overwritten
2025-05-30 09:59:27.917 | INFO     | __main__:get_user_confirmation:265 -    • Azure costs will be incurred for compute resources
2025-05-30 09:59:27.917 | INFO     | __main__:get_user_confirmation:266 -    • The deployment process may take 15-30 minutes
2025-05-30 09:59:27.917 | INFO     | __main__:get_user_confirmation:268 - ==================================================
2025-05-30 10:00:24.334 | INFO     | __main__:get_user_confirmation:277 - ✅ User confirmed deployment. Proceeding...
2025-05-30 10:00:24.334 | INFO     | __main__:main:956 - 🔧 STEP 4: Initializing Azure ML deployer...
2025-05-30 10:00:24.335 | INFO     | __main__:__init__:310 - Initialized AzureMLDeployer with configuration
2025-05-30 10:00:24.335 | INFO     | __main__:main:958 - ✅ Deployer initialized successfully
2025-05-30 10:00:24.335 | INFO     | __main__:main:961 - 🚀 STEP 5: Executing deployment...
2025-05-30 10:00:24.335 | INFO     | __main__:main:962 - ⚠️  WARNING: Irreversible deployment operations will now begin!
2025-05-30 10:00:24.335 | INFO     | __main__:deploy_with_retry:809 - 🔄 DEPLOYMENT EXECUTION PHASE
2025-05-30 10:00:24.335 | INFO     | __main__:deploy_with_retry:810 - ==================================================
2025-05-30 10:00:24.335 | INFO     | __main__:deploy_with_retry:811 - Starting deployment attempt 1/1
2025-05-30 10:00:24.335 | INFO     | __main__:deploy_with_retry:817 - 📦 STEP 1/5: Model Registration
2025-05-30 10:00:24.335 | INFO     | __main__:register_model:428 - 🔍 MODEL REGISTRATION PHASE
2025-05-30 10:00:24.335 | INFO     | __main__:register_model:429 - ========================================
2025-05-30 10:00:24.335 | INFO     | __main__:register_model:430 - Checking for existing model: ps-uat-ca-tstar
2025-05-30 10:00:24.335 | INFO     | __main__:_create_ml_client:330 - Creating Azure ML client connection
2025-05-30 10:00:24.975 | INFO     | __main__:_create_ml_client:345 - Successfully connected to workspace: ps-uat-mlw-claimsautoml
2025-05-30 10:00:25.379 | INFO     | __main__:register_model:442 - ℹ️  Model ps-uat-ca-tstar not found, creating new registration
2025-05-30 10:00:25.379 | INFO     | __main__:register_model:446 - 📁 Validating model file: /home/<USER>/repos/autolodge_retrained_deploy/data/models/autolodge.h5
2025-05-30 10:00:25.379 | INFO     | __main__:register_model:453 - ✅ Model file validation successful:
2025-05-30 10:00:25.379 | INFO     | __main__:register_model:454 -    File size: 60.37 MB
2025-05-30 10:00:25.379 | INFO     | __main__:register_model:455 -    Full path: /home/<USER>/repos/autolodge_retrained_deploy/data/models/autolodge.h5
2025-05-30 10:00:25.380 | INFO     | __main__:register_model:465 - 🚀 Registering new model from path: /home/<USER>/repos/autolodge_retrained_deploy/data/models/autolodge.h5
2025-05-30 10:00:25.380 | INFO     | __main__:register_model:466 -    Model name: ps-uat-ca-tstar
2025-05-30 10:00:25.380 | INFO     | __main__:register_model:467 -    Model type: custom_model
2025-05-30 10:00:33.002 | INFO     | __main__:register_model:471 - ✅ Successfully registered model:
2025-05-30 10:00:33.002 | INFO     | __main__:register_model:472 -    Name: ps-uat-ca-tstar
2025-05-30 10:00:33.003 | INFO     | __main__:register_model:473 -    Version: 1
2025-05-30 10:00:33.003 | INFO     | __main__:register_model:474 -    ID: /subscriptions/b15ae5d0-8f07-4cfb-aca3-508d38e9d983/resourceGroups/ps-uat-rg-claimsautoml/providers/Microsoft.MachineLearningServices/workspaces/ps-uat-mlw-claimsautoml/models/ps-uat-ca-tstar/versions/1
2025-05-30 10:00:33.003 | INFO     | __main__:register_model:475 - ========================================
2025-05-30 10:00:33.003 | INFO     | __main__:deploy_with_retry:821 - 🌍 STEP 2/5: Environment Setup
2025-05-30 10:00:33.003 | INFO     | __main__:create_environment:495 - 🌍 ENVIRONMENT SETUP PHASE
2025-05-30 10:00:33.003 | INFO     | __main__:create_environment:496 - ========================================
2025-05-30 10:00:33.003 | INFO     | __main__:create_environment:497 - Checking for existing environment: ps-uat-ca-tstar
2025-05-30 10:00:33.489 | INFO     | __main__:create_environment:509 - ℹ️  Environment ps-uat-ca-tstar not found, creating new one
2025-05-30 10:00:33.490 | INFO     | __main__:create_environment:513 - 📁 Validating conda file: /home/<USER>/repos/autolodge_retrained_deploy/configs/ps-uat-claimsauto-tstar.yaml
2025-05-30 10:00:33.490 | INFO     | __main__:create_environment:520 - ✅ Conda file validation successful:
2025-05-30 10:00:33.490 | INFO     | __main__:create_environment:521 -    File size: 472 bytes
2025-05-30 10:00:33.490 | INFO     | __main__:create_environment:522 -    Full path: /home/<USER>/repos/autolodge_retrained_deploy/configs/ps-uat-claimsauto-tstar.yaml
2025-05-30 10:00:33.490 | INFO     | __main__:create_environment:528 -    Conda file preview (first 10 lines):
2025-05-30 10:00:33.490 | INFO     | __main__:create_environment:530 -      1: channels:
2025-05-30 10:00:33.490 | INFO     | __main__:create_environment:530 -      2: - Microsoft
2025-05-30 10:00:33.490 | INFO     | __main__:create_environment:530 -      3: - defaults
2025-05-30 10:00:33.490 | INFO     | __main__:create_environment:530 -      4: dependencies:
2025-05-30 10:00:33.490 | INFO     | __main__:create_environment:530 -      5: - pip
2025-05-30 10:00:33.490 | INFO     | __main__:create_environment:530 -      6: - python=3.9
2025-05-30 10:00:33.491 | INFO     | __main__:create_environment:530 -      7: - pip:
2025-05-30 10:00:33.491 | INFO     | __main__:create_environment:530 -      8: - azure-ai-ml==1.27.1
2025-05-30 10:00:33.491 | INFO     | __main__:create_environment:530 -      9: - azure-identity==1.23.0
2025-05-30 10:00:33.491 | INFO     | __main__:create_environment:530 -      10: - azureml-inference-server-http==1.4.0
2025-05-30 10:00:33.491 | INFO     | __main__:create_environment:532 -      ... (file continues)
2025-05-30 10:00:33.494 | INFO     | __main__:create_environment:545 - 🚀 Creating new environment:
2025-05-30 10:00:33.494 | INFO     | __main__:create_environment:546 -    Environment name: ps-uat-ca-tstar
2025-05-30 10:00:33.494 | INFO     | __main__:create_environment:547 -    Base image: mcr.microsoft.com/azureml/openmpi3.1.2-ubuntu18.04
2025-05-30 10:00:33.494 | INFO     | __main__:create_environment:548 -    Conda file: /home/<USER>/repos/autolodge_retrained_deploy/configs/ps-uat-claimsauto-tstar.yaml
2025-05-30 10:00:40.576 | INFO     | __main__:create_environment:552 - ✅ Successfully created environment:
2025-05-30 10:00:40.576 | INFO     | __main__:create_environment:553 -    Name: ps-uat-ca-tstar
2025-05-30 10:00:40.576 | INFO     | __main__:create_environment:554 -    Version: 1
2025-05-30 10:00:40.576 | INFO     | __main__:create_environment:555 -    ID: /subscriptions/b15ae5d0-8f07-4cfb-aca3-508d38e9d983/resourceGroups/ps-uat-rg-claimsautoml/providers/Microsoft.MachineLearningServices/workspaces/ps-uat-mlw-claimsautoml/environments/ps-uat-ca-tstar/versions/1
2025-05-30 10:00:40.576 | INFO     | __main__:create_environment:556 - ========================================
2025-05-30 10:00:40.576 | INFO     | __main__:deploy_with_retry:825 - 🌐 STEP 3/5: Endpoint Creation
2025-05-30 10:00:40.576 | INFO     | __main__:create_endpoint:576 - Checking for existing endpoint: ps-uat-ca-tstar
2025-05-30 10:00:40.673 | INFO     | __main__:create_endpoint:596 - Endpoint ps-uat-ca-tstar not found, creating new one
2025-05-30 10:00:40.675 | INFO     | __main__:create_endpoint:606 - Creating new endpoint: ps-uat-ca-tstar
2025-05-30 10:00:42.822 | INFO     | __main__:_wait_for_operation_completion:389 - Starting endpoint creation operation (timeout: 1800s)
2025-05-30 10:00:42.822 | INFO     | __main__:_wait_for_operation_completion:402 - endpoint creation in progress... (0s elapsed)
2025-05-30 10:01:12.852 | INFO     | __main__:_wait_for_operation_completion:402 - endpoint creation in progress... (30s elapsed)
2025-05-30 10:01:42.883 | INFO     | __main__:_wait_for_operation_completion:402 - endpoint creation in progress... (60s elapsed)
2025-05-30 10:01:47.888 | INFO     | __main__:_wait_for_operation_completion:408 - endpoint creation completed successfully in 65.1 seconds
2025-05-30 10:01:48.052 | INFO     | __main__:create_endpoint:614 - Successfully created endpoint: ps-uat-ca-tstar
2025-05-30 10:01:48.054 | INFO     | __main__:deploy_with_retry:829 - 🚀 STEP 4/5: Deployment Creation
2025-05-30 10:01:48.054 | INFO     | __main__:create_deployment:652 - 🚀 DEPLOYMENT CREATION PHASE
2025-05-30 10:01:48.054 | INFO     | __main__:create_deployment:653 - ========================================
2025-05-30 10:01:48.055 | INFO     | __main__:create_deployment:659 - 📁 Validating deployment files:
2025-05-30 10:01:48.055 | INFO     | __main__:create_deployment:660 -    Scoring script: /home/<USER>/repos/autolodge_retrained_deploy/Python/score.py
2025-05-30 10:01:48.055 | INFO     | __main__:create_deployment:661 -    Code directory: /home/<USER>/repos/autolodge_retrained_deploy/Python
2025-05-30 10:01:48.055 | INFO     | __main__:create_deployment:668 - ✅ Scoring script validation successful:
2025-05-30 10:01:48.056 | INFO     | __main__:create_deployment:669 -    File size: 34.88 KB
2025-05-30 10:01:48.056 | INFO     | __main__:create_deployment:678 -    score.py: ✅ EXISTS
2025-05-30 10:01:48.056 | INFO     | __main__:create_deployment:683 -    resources/: ✅ EXISTS
2025-05-30 10:01:48.057 | INFO     | __main__:create_deployment:683 -    logs/: ✅ EXISTS
2025-05-30 10:01:48.057 | INFO     | __main__:create_deployment:685 - 🔧 Deployment configuration:
2025-05-30 10:01:48.057 | INFO     | __main__:create_deployment:686 -    Deployment name: ps-uat-ca-tstar
2025-05-30 10:01:48.057 | INFO     | __main__:create_deployment:687 -    Endpoint name: ps-uat-ca-tstar
2025-05-30 10:01:48.057 | INFO     | __main__:create_deployment:688 -    Model: ps-uat-ca-tstar (v1)
2025-05-30 10:01:48.057 | INFO     | __main__:create_deployment:689 -    Environment: ps-uat-ca-tstar (v1)
2025-05-30 10:01:48.057 | INFO     | __main__:create_deployment:690 -    Instance type: Standard_DS3_v2
2025-05-30 10:01:48.057 | INFO     | __main__:create_deployment:691 -    Instance count: 1
2025-05-30 10:01:48.057 | INFO     | __main__:create_deployment:704 - 🏷️  Environment variables:
2025-05-30 10:01:48.057 | INFO     | __main__:create_deployment:706 -    PYTHONPATH: /var/azureml-app/autolodge_retrained_deploy
2025-05-30 10:01:48.058 | INFO     | __main__:create_deployment:708 - 🏷️  Deployment tags:
2025-05-30 10:01:48.058 | INFO     | __main__:create_deployment:710 -    DeploymentDate: 2025-05-30T10:01:48.057876+10:00
2025-05-30 10:01:48.058 | INFO     | __main__:create_deployment:710 -    ModelName: ps-uat-ca-tstar
2025-05-30 10:01:48.058 | INFO     | __main__:create_deployment:710 -    ModelVersion: 1
2025-05-30 10:01:48.058 | INFO     | __main__:create_deployment:710 -    EnvironmentName: ps-uat-ca-tstar
2025-05-30 10:01:48.058 | INFO     | __main__:create_deployment:710 -    EnvironmentVersion: 1
2025-05-30 10:01:48.058 | INFO     | __main__:create_deployment:710 -    CreatedBy: AzureMLDeployer
2025-05-30 10:01:48.058 | INFO     | __main__:create_deployment:725 - 🚀 Starting deployment creation...
2025-05-30 10:01:48.059 | WARNING  | __main__:create_deployment:726 - ⏱️  This operation may take 15-30 minutes to complete
2025-05-30 10:01:54.210 | INFO     | __main__:_wait_for_operation_completion:389 - Starting deployment creation operation (timeout: 1800s)
2025-05-30 10:01:54.210 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (0s elapsed)
2025-05-30 10:02:24.235 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (30s elapsed)
2025-05-30 10:02:54.265 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (60s elapsed)
2025-05-30 10:03:24.291 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (90s elapsed)
2025-05-30 10:03:54.310 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (120s elapsed)
2025-05-30 10:04:24.338 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (150s elapsed)
2025-05-30 10:04:54.367 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (180s elapsed)
2025-05-30 10:05:24.397 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (210s elapsed)
2025-05-30 10:05:54.421 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (240s elapsed)
2025-05-30 10:06:24.447 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (270s elapsed)
2025-05-30 10:06:54.477 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (300s elapsed)
2025-05-30 10:07:24.506 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (330s elapsed)
2025-05-30 10:07:54.536 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (360s elapsed)
2025-05-30 10:08:24.563 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (390s elapsed)
2025-05-30 10:08:54.592 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (420s elapsed)
2025-05-30 10:09:24.615 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (450s elapsed)
2025-05-30 10:09:54.640 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (480s elapsed)
2025-05-30 10:10:24.660 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (510s elapsed)
2025-05-30 10:10:54.686 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (540s elapsed)
2025-05-30 10:11:24.714 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (571s elapsed)
2025-05-30 10:11:54.743 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (601s elapsed)
2025-05-30 10:12:24.773 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (631s elapsed)
2025-05-30 10:12:54.800 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (661s elapsed)
2025-05-30 10:13:24.826 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (691s elapsed)
2025-05-30 10:13:54.850 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (721s elapsed)
2025-05-30 10:14:24.880 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (751s elapsed)
2025-05-30 10:14:54.905 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (781s elapsed)
2025-05-30 10:15:24.935 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (811s elapsed)
2025-05-30 10:15:54.962 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (841s elapsed)
2025-05-30 10:16:24.986 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (871s elapsed)
2025-05-30 10:16:55.011 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (901s elapsed)
2025-05-30 10:17:25.040 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (931s elapsed)
2025-05-30 10:17:55.070 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (961s elapsed)
2025-05-30 10:18:05.081 | INFO     | __main__:_wait_for_operation_completion:408 - deployment creation completed successfully in 970.9 seconds
2025-05-30 10:18:05.236 | INFO     | __main__:create_deployment:739 - ✅ Successfully created deployment:
2025-05-30 10:18:05.236 | INFO     | __main__:create_deployment:740 -    Name: ps-uat-ca-tstar
2025-05-30 10:18:05.236 | INFO     | __main__:create_deployment:741 -    Endpoint: ps-uat-ca-tstar
2025-05-30 10:18:05.236 | INFO     | __main__:create_deployment:742 -    Status: Succeeded
2025-05-30 10:18:05.237 | INFO     | __main__:create_deployment:743 - ========================================
2025-05-30 10:18:05.237 | INFO     | __main__:deploy_with_retry:833 - 🚦 STEP 5/5: Traffic Configuration
2025-05-30 10:18:05.237 | INFO     | __main__:configure_traffic:772 - Configuring traffic for deployment: ps-uat-ca-tstar
2025-05-30 10:18:06.154 | INFO     | __main__:_wait_for_operation_completion:389 - Starting traffic configuration operation (timeout: 1800s)
2025-05-30 10:18:06.154 | INFO     | __main__:_wait_for_operation_completion:402 - traffic configuration in progress... (0s elapsed)
2025-05-30 10:18:36.179 | INFO     | __main__:_wait_for_operation_completion:402 - traffic configuration in progress... (30s elapsed)
2025-05-30 10:18:41.185 | INFO     | __main__:_wait_for_operation_completion:408 - traffic configuration completed successfully in 35.0 seconds
2025-05-30 10:18:41.373 | INFO     | __main__:configure_traffic:787 - Traffic configuration completed: {'ps-uat-ca-tstar': 100}
2025-05-30 10:18:41.378 | INFO     | __main__:deploy_with_retry:836 - ✅ All deployment steps completed successfully!
2025-05-30 10:18:41.379 | INFO     | __main__:deploy_with_retry:837 - ==================================================
2025-05-30 10:18:41.379 | INFO     | __main__:main:966 - 🎉 DEPLOYMENT COMPLETED SUCCESSFULLY!
2025-05-30 10:18:41.380 | INFO     | __main__:main:967 - ============================================================
2025-05-30 10:18:41.380 | INFO     | __main__:main:968 - FINAL DEPLOYMENT SUMMARY
2025-05-30 10:18:41.381 | INFO     | __main__:main:969 - ============================================================
2025-05-30 10:18:41.381 | INFO     | __main__:main:970 - ✅ Endpoint: ps-uat-ca-tstar
2025-05-30 10:18:41.381 | INFO     | __main__:main:971 - ✅ Deployment: ps-uat-ca-tstar
2025-05-30 10:18:41.382 | INFO     | __main__:main:972 - ✅ Traffic Configuration: {'ps-uat-ca-tstar': 100}
2025-05-30 10:18:41.382 | INFO     | __main__:main:973 - ✅ Instance Type: Standard_DS3_v2
2025-05-30 10:18:41.383 | INFO     | __main__:main:974 - ✅ Instance Count: 1
2025-05-30 10:18:41.383 | INFO     | __main__:main:975 - ✅ Workspace: ps-uat-mlw-claimsautoml
2025-05-30 10:18:41.383 | INFO     | __main__:main:976 - ✅ Resource Group: ps-uat-rg-claimsautoml
2025-05-30 10:18:41.384 | INFO     | __main__:main:977 - ============================================================
2025-05-30 10:18:41.384 | INFO     | __main__:main:980 - 📋 NEXT STEPS:
2025-05-30 10:18:41.384 | INFO     | __main__:main:981 -    1. Test the deployed endpoint with sample data
2025-05-30 10:18:41.384 | INFO     | __main__:main:982 -    2. Monitor deployment performance and logs
2025-05-30 10:18:41.384 | INFO     | __main__:main:983 -    3. Update traffic allocation if needed
2025-05-30 10:18:41.385 | INFO     | __main__:main:984 -    4. Set up monitoring and alerting
2025-05-30 10:18:41.388 | INFO     | __main__:main:985 - ============================================================
2025-05-30 10:18:41.388 | SUCCESS  | __main__:main:987 - 🎉 Deployment completed successfully!
2025-05-30 10:18:41.388 | INFO     | __main__:deployment_context:920 - Deployment context completed successfully
2025-05-30 10:18:41.388 | INFO     | __main__:deployment_context:925 - Deployment context cleanup completed
