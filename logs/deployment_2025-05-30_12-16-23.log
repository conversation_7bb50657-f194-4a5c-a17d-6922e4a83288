2025-05-30 12:16:23.320 | INFO     | __main__:setup_logging:98 - Logging configured - Log file: logs/deployment_2025-05-30_12-16-23.log
2025-05-30 12:16:23.321 | INFO     | __main__:deployment_context:954 - Starting deployment context
2025-05-30 12:16:23.321 | INFO     | __main__:main:974 - 🚀 AZURE ML DEPLOYMENT SCRIPT STARTED
2025-05-30 12:16:23.321 | INFO     | __main__:main:975 - ============================================================
2025-05-30 12:16:23.322 | INFO     | __main__:main:978 - 📋 STEP 1: Loading and validating configuration...
2025-05-30 12:16:23.324 | INFO     | __main__:load_and_validate_environment:942 - Environment configuration loaded and validated successfully
2025-05-30 12:16:23.324 | INFO     | __main__:main:980 - ✅ Configuration loaded successfully
2025-05-30 12:16:23.324 | INFO     | __main__:main:983 - 📊 STEP 2: Displaying deployment summary...
2025-05-30 12:16:23.324 | INFO     | __main__:display_deployment_summary:176 - ================================================================================
2025-05-30 12:16:23.324 | INFO     | __main__:display_deployment_summary:177 - DEPLOYMENT CONFIGURATION SUMMARY
2025-05-30 12:16:23.324 | INFO     | __main__:display_deployment_summary:178 - ================================================================================
2025-05-30 12:16:23.324 | INFO     | __main__:display_deployment_summary:181 - 🔧 Azure Environment:
2025-05-30 12:16:23.324 | INFO     | __main__:display_deployment_summary:182 -    Subscription ID: b15ae5d0-8f07-4cfb-aca3-508d38e9d983
2025-05-30 12:16:23.324 | INFO     | __main__:display_deployment_summary:183 -    Resource Group:  ps-preprod-rg-claimsauto
2025-05-30 12:16:23.324 | INFO     | __main__:display_deployment_summary:184 -    Workspace:       ps-preprod-ml-claimsauto
2025-05-30 12:16:23.324 | INFO     | __main__:display_deployment_summary:187 - 🚀 Deployment Configuration:
2025-05-30 12:16:23.324 | INFO     | __main__:display_deployment_summary:188 -    Model Name:      ps-preprod-ca-tstar
2025-05-30 12:16:23.324 | INFO     | __main__:display_deployment_summary:189 -    Environment:     ps-preprod-ca-tstar
2025-05-30 12:16:23.324 | INFO     | __main__:display_deployment_summary:190 -    Endpoint Name:   ps-preprod-ca-tstar
2025-05-30 12:16:23.325 | INFO     | __main__:display_deployment_summary:191 -    Deployment Name: ps-preprod-ca-tstar
2025-05-30 12:16:23.325 | INFO     | __main__:display_deployment_summary:194 - 💻 Infrastructure:
2025-05-30 12:16:23.325 | INFO     | __main__:display_deployment_summary:195 -    Instance Type:   STANDARD_NC4AS_T4_V3
2025-05-30 12:16:23.325 | INFO     | __main__:display_deployment_summary:196 -    Instance Count:  1
2025-05-30 12:16:23.325 | INFO     | __main__:display_deployment_summary:197 -    Timeout:         1800 seconds
2025-05-30 12:16:23.325 | INFO     | __main__:display_deployment_summary:198 -    Max Retries:     1
2025-05-30 12:16:23.325 | INFO     | __main__:display_deployment_summary:201 - 📁 File Validation:
2025-05-30 12:16:23.325 | INFO     | __main__:display_deployment_summary:207 -    Model File:      ✅ EXISTS (60.4 MB)
2025-05-30 12:16:23.325 | INFO     | __main__:display_deployment_summary:208 -    Model Path:      /home/<USER>/repos/autolodge_retrained_deploy/data/models/autolodge.h5
2025-05-30 12:16:23.325 | INFO     | __main__:display_deployment_summary:213 -    Conda File:      ✅ EXISTS
2025-05-30 12:16:23.325 | INFO     | __main__:display_deployment_summary:214 -    Conda Path:      /home/<USER>/repos/autolodge_retrained_deploy/configs/autolodge.yaml
2025-05-30 12:16:23.325 | INFO     | __main__:display_deployment_summary:219 -    Scoring Script:  ✅ EXISTS
2025-05-30 12:16:23.326 | INFO     | __main__:display_deployment_summary:220 -    Script Path:     /home/<USER>/repos/autolodge_retrained_deploy/Python/score.py
2025-05-30 12:16:23.326 | INFO     | __main__:display_deployment_summary:223 - 🔐 Environment Variables:
2025-05-30 12:16:23.326 | INFO     | __main__:display_deployment_summary:237 -    AZURE_SUBSCRIPTION_ID: ✅ b15ae5d0...
2025-05-30 12:16:23.326 | INFO     | __main__:display_deployment_summary:237 -    AZURE_RESOURCE_GROUP: ✅ ps-preprod-rg-claimsauto
2025-05-30 12:16:23.326 | INFO     | __main__:display_deployment_summary:237 -    AZURE_ML_WORKSPACE_NAME: ✅ ps-preprod-ml-claimsauto
2025-05-30 12:16:23.326 | INFO     | __main__:display_deployment_summary:237 -    MODEL_NAME: ✅ ps-preprod-ca-tstar
2025-05-30 12:16:23.326 | INFO     | __main__:display_deployment_summary:237 -    ENV_NAME: ✅ ps-preprod-ca-tstar
2025-05-30 12:16:23.326 | INFO     | __main__:display_deployment_summary:237 -    ENDPOINT_NAME: ✅ ps-preprod-ca-tstar
2025-05-30 12:16:23.326 | INFO     | __main__:display_deployment_summary:237 -    DEPLOYMENT_NAME: ✅ ps-preprod-ca-tstar
2025-05-30 12:16:23.326 | INFO     | __main__:display_deployment_summary:239 - ================================================================================
2025-05-30 12:16:23.326 | INFO     | __main__:main:987 - 🔐 STEP 3: User confirmation required...
2025-05-30 12:16:23.326 | INFO     | __main__:get_user_confirmation:252 - ⚠️  DEPLOYMENT CONFIRMATION REQUIRED
2025-05-30 12:16:23.326 | INFO     | __main__:get_user_confirmation:253 - ==================================================
2025-05-30 12:16:23.326 | WARNING  | __main__:get_user_confirmation:256 - 🎯 Target Environment: ps-preprod-ml-claimsauto
2025-05-30 12:16:23.326 | WARNING  | __main__:get_user_confirmation:257 - 🚀 Deployment Name: ps-preprod-ca-tstar
2025-05-30 12:16:23.327 | WARNING  | __main__:get_user_confirmation:258 - 📦 Model: ps-preprod-ca-tstar
2025-05-30 12:16:23.327 | WARNING  | __main__:get_user_confirmation:259 - 💰 Instance Type: STANDARD_NC4AS_T4_V3 (Count: 1)
2025-05-30 12:16:23.327 | INFO     | __main__:get_user_confirmation:262 - ⚠️  Potential Risks:
2025-05-30 12:16:23.327 | INFO     | __main__:get_user_confirmation:263 -    • This will create/update Azure ML resources
2025-05-30 12:16:23.327 | INFO     | __main__:get_user_confirmation:264 -    • Existing deployments with the same name may be overwritten
2025-05-30 12:16:23.327 | INFO     | __main__:get_user_confirmation:265 -    • Azure costs will be incurred for compute resources
2025-05-30 12:16:23.327 | INFO     | __main__:get_user_confirmation:266 -    • The deployment process may take 15-30 minutes
2025-05-30 12:16:23.327 | INFO     | __main__:get_user_confirmation:268 - ==================================================
2025-05-30 12:16:25.532 | INFO     | __main__:get_user_confirmation:277 - ✅ User confirmed deployment. Proceeding...
2025-05-30 12:16:25.533 | INFO     | __main__:main:993 - 🔧 STEP 4: Initializing Azure ML deployer...
2025-05-30 12:16:25.533 | INFO     | __main__:__init__:310 - Initialized AzureMLDeployer with configuration
2025-05-30 12:16:25.533 | INFO     | __main__:main:995 - ✅ Deployer initialized successfully
2025-05-30 12:16:25.533 | INFO     | __main__:main:998 - 🚀 STEP 5: Executing deployment...
2025-05-30 12:16:25.533 | INFO     | __main__:main:999 - ⚠️  WARNING: Irreversible deployment operations will now begin!
2025-05-30 12:16:25.533 | INFO     | __main__:deploy_with_retry:846 - 🔄 DEPLOYMENT EXECUTION PHASE
2025-05-30 12:16:25.533 | INFO     | __main__:deploy_with_retry:847 - ==================================================
2025-05-30 12:16:25.533 | INFO     | __main__:deploy_with_retry:848 - Starting deployment attempt 1/1
2025-05-30 12:16:25.533 | INFO     | __main__:deploy_with_retry:854 - 📦 STEP 1/5: Model Registration
2025-05-30 12:16:25.533 | INFO     | __main__:register_model:428 - 🔍 MODEL REGISTRATION PHASE
2025-05-30 12:16:25.533 | INFO     | __main__:register_model:429 - ========================================
2025-05-30 12:16:25.533 | INFO     | __main__:register_model:430 - Checking for existing model: ps-preprod-ca-tstar
2025-05-30 12:16:25.533 | INFO     | __main__:_create_ml_client:330 - Creating Azure ML client connection
2025-05-30 12:16:26.131 | INFO     | __main__:_create_ml_client:345 - Successfully connected to workspace: ps-preprod-ml-claimsauto
2025-05-30 12:16:26.473 | INFO     | __main__:register_model:435 - ✅ Found existing model: ps-preprod-ca-tstar (version: 1)
2025-05-30 12:16:26.474 | INFO     | __main__:register_model:436 -    Model ID: /subscriptions/b15ae5d0-8f07-4cfb-aca3-508d38e9d983/resourceGroups/ps-preprod-rg-claimsauto/providers/Microsoft.MachineLearningServices/workspaces/ps-preprod-ml-claimsauto/models/ps-preprod-ca-tstar/versions/1
2025-05-30 12:16:26.474 | INFO     | __main__:register_model:471 -    Created: 2025-05-30 11:49:52 AEST
2025-05-30 12:16:26.475 | INFO     | __main__:register_model:475 -    Description: Model registered on 2025-05-30T11:49:48.024090+10:00
2025-05-30 12:16:26.475 | INFO     | __main__:deploy_with_retry:858 - 🌍 STEP 2/5: Environment Setup
2025-05-30 12:16:26.475 | INFO     | __main__:create_environment:532 - 🌍 ENVIRONMENT SETUP PHASE
2025-05-30 12:16:26.475 | INFO     | __main__:create_environment:533 - ========================================
2025-05-30 12:16:26.475 | INFO     | __main__:create_environment:534 - Checking for existing environment: ps-preprod-ca-tstar
2025-05-30 12:16:27.323 | INFO     | __main__:create_environment:539 - ✅ Found existing environment: ps-preprod-ca-tstar (version: 1)
2025-05-30 12:16:27.324 | INFO     | __main__:create_environment:540 -    Environment ID: /subscriptions/b15ae5d0-8f07-4cfb-aca3-508d38e9d983/resourceGroups/ps-preprod-rg-claimsauto/providers/Microsoft.MachineLearningServices/workspaces/ps-preprod-ml-claimsauto/environments/ps-preprod-ca-tstar/versions/1
2025-05-30 12:16:27.324 | INFO     | __main__:create_environment:541 -    Base Image: mcr.microsoft.com/azureml/openmpi3.1.2-ubuntu18.04
2025-05-30 12:16:27.324 | INFO     | __main__:create_environment:542 -    Description: Environment created on 2025-05-30T11:49:54.212292+10:00
2025-05-30 12:16:27.324 | INFO     | __main__:deploy_with_retry:862 - 🌐 STEP 3/5: Endpoint Creation
2025-05-30 12:16:27.324 | INFO     | __main__:create_endpoint:613 - Checking for existing endpoint: ps-preprod-ca-tstar
2025-05-30 12:16:27.509 | INFO     | __main__:create_endpoint:618 - Found existing endpoint: ps-preprod-ca-tstar
2025-05-30 12:16:27.510 | INFO     | __main__:deploy_with_retry:866 - 🚀 STEP 4/5: Deployment Creation
2025-05-30 12:16:27.510 | INFO     | __main__:create_deployment:689 - 🚀 DEPLOYMENT CREATION PHASE
2025-05-30 12:16:27.510 | INFO     | __main__:create_deployment:690 - ========================================
2025-05-30 12:16:27.510 | INFO     | __main__:create_deployment:696 - 📁 Validating deployment files:
2025-05-30 12:16:27.510 | INFO     | __main__:create_deployment:697 -    Scoring script: /home/<USER>/repos/autolodge_retrained_deploy/Python/score.py
2025-05-30 12:16:27.510 | INFO     | __main__:create_deployment:698 -    Code directory: /home/<USER>/repos/autolodge_retrained_deploy/Python
2025-05-30 12:16:27.510 | INFO     | __main__:create_deployment:705 - ✅ Scoring script validation successful:
2025-05-30 12:16:27.510 | INFO     | __main__:create_deployment:706 -    File size: 34.88 KB
2025-05-30 12:16:27.510 | INFO     | __main__:create_deployment:715 -    score.py: ✅ EXISTS
2025-05-30 12:16:27.510 | INFO     | __main__:create_deployment:720 -    resources/: ✅ EXISTS
2025-05-30 12:16:27.510 | INFO     | __main__:create_deployment:720 -    logs/: ✅ EXISTS
2025-05-30 12:16:27.511 | INFO     | __main__:create_deployment:722 - 🔧 Deployment configuration:
2025-05-30 12:16:27.511 | INFO     | __main__:create_deployment:723 -    Deployment name: ps-preprod-ca-tstar
2025-05-30 12:16:27.511 | INFO     | __main__:create_deployment:724 -    Endpoint name: ps-preprod-ca-tstar
2025-05-30 12:16:27.511 | INFO     | __main__:create_deployment:725 -    Model: ps-preprod-ca-tstar (v1)
2025-05-30 12:16:27.511 | INFO     | __main__:create_deployment:726 -    Environment: ps-preprod-ca-tstar (v1)
2025-05-30 12:16:27.511 | INFO     | __main__:create_deployment:727 -    Instance type: STANDARD_NC4AS_T4_V3
2025-05-30 12:16:27.511 | INFO     | __main__:create_deployment:728 -    Instance count: 1
2025-05-30 12:16:27.511 | INFO     | __main__:create_deployment:741 - 🏷️  Environment variables:
2025-05-30 12:16:27.511 | INFO     | __main__:create_deployment:743 -    PYTHONPATH: /var/azureml-app/autolodge_retrained_deploy
2025-05-30 12:16:27.511 | INFO     | __main__:create_deployment:745 - 🏷️  Deployment tags:
2025-05-30 12:16:27.511 | INFO     | __main__:create_deployment:747 -    DeploymentDate: 2025-05-30T12:16:27.511492+10:00
2025-05-30 12:16:27.511 | INFO     | __main__:create_deployment:747 -    ModelName: ps-preprod-ca-tstar
2025-05-30 12:16:27.511 | INFO     | __main__:create_deployment:747 -    ModelVersion: 1
2025-05-30 12:16:27.511 | INFO     | __main__:create_deployment:747 -    EnvironmentName: ps-preprod-ca-tstar
2025-05-30 12:16:27.511 | INFO     | __main__:create_deployment:747 -    EnvironmentVersion: 1
2025-05-30 12:16:27.512 | INFO     | __main__:create_deployment:747 -    CreatedBy: AzureMLDeployer
2025-05-30 12:16:27.512 | INFO     | __main__:create_deployment:762 - 🚀 Starting deployment creation...
2025-05-30 12:16:27.512 | WARNING  | __main__:create_deployment:763 - ⏱️  This operation may take 15-30 minutes to complete
2025-05-30 12:16:32.464 | INFO     | __main__:_wait_for_operation_completion:389 - Starting deployment creation operation (timeout: 1800s)
2025-05-30 12:16:32.464 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (0s elapsed)
2025-05-30 12:17:02.491 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (30s elapsed)
2025-05-30 12:17:32.518 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (60s elapsed)
2025-05-30 12:18:02.547 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (90s elapsed)
2025-05-30 12:18:32.569 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (120s elapsed)
2025-05-30 12:19:02.595 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (150s elapsed)
2025-05-30 12:19:32.625 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (180s elapsed)
2025-05-30 12:20:02.655 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (210s elapsed)
2025-05-30 12:20:32.685 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (240s elapsed)
2025-05-30 12:21:02.715 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (270s elapsed)
2025-05-30 12:21:32.745 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (300s elapsed)
2025-05-30 12:22:02.773 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (330s elapsed)
2025-05-30 12:22:32.799 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (360s elapsed)
2025-05-30 12:23:02.826 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (390s elapsed)
2025-05-30 12:23:32.853 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (420s elapsed)
2025-05-30 12:24:02.884 | INFO     | __main__:_wait_for_operation_completion:402 - deployment creation in progress... (450s elapsed)
2025-05-30 12:24:32.908 | INFO     | __main__:_wait_for_operation_completion:408 - deployment creation completed successfully in 480.4 seconds
2025-05-30 12:24:32.977 | INFO     | __main__:create_deployment:776 - ✅ Successfully created deployment:
2025-05-30 12:24:32.977 | INFO     | __main__:create_deployment:777 -    Name: ps-preprod-ca-tstar
2025-05-30 12:24:32.978 | INFO     | __main__:create_deployment:778 -    Endpoint: ps-preprod-ca-tstar
2025-05-30 12:24:32.978 | INFO     | __main__:create_deployment:779 -    Status: Succeeded
2025-05-30 12:24:32.978 | INFO     | __main__:create_deployment:780 - ========================================
2025-05-30 12:24:32.978 | INFO     | __main__:deploy_with_retry:870 - 🚦 STEP 5/5: Traffic Configuration
2025-05-30 12:24:32.978 | INFO     | __main__:configure_traffic:809 - Configuring traffic for deployment: ps-preprod-ca-tstar
2025-05-30 12:24:34.681 | INFO     | __main__:_wait_for_operation_completion:389 - Starting traffic configuration operation (timeout: 1800s)
2025-05-30 12:24:34.682 | INFO     | __main__:_wait_for_operation_completion:402 - traffic configuration in progress... (0s elapsed)
2025-05-30 12:25:04.707 | INFO     | __main__:_wait_for_operation_completion:402 - traffic configuration in progress... (30s elapsed)
2025-05-30 12:25:09.713 | INFO     | __main__:_wait_for_operation_completion:408 - traffic configuration completed successfully in 35.0 seconds
2025-05-30 12:25:09.868 | INFO     | __main__:configure_traffic:824 - Traffic configuration completed: {'ps-preprod-ca-tstar': 100}
2025-05-30 12:25:09.868 | INFO     | __main__:deploy_with_retry:873 - ✅ All deployment steps completed successfully!
2025-05-30 12:25:09.868 | INFO     | __main__:deploy_with_retry:874 - ==================================================
2025-05-30 12:25:09.868 | INFO     | __main__:main:1003 - 🎉 DEPLOYMENT COMPLETED SUCCESSFULLY!
2025-05-30 12:25:09.868 | INFO     | __main__:main:1004 - ============================================================
2025-05-30 12:25:09.868 | INFO     | __main__:main:1005 - FINAL DEPLOYMENT SUMMARY
2025-05-30 12:25:09.868 | INFO     | __main__:main:1006 - ============================================================
2025-05-30 12:25:09.868 | INFO     | __main__:main:1007 - ✅ Endpoint: ps-preprod-ca-tstar
2025-05-30 12:25:09.869 | INFO     | __main__:main:1008 - ✅ Deployment: ps-preprod-ca-tstar
2025-05-30 12:25:09.869 | INFO     | __main__:main:1009 - ✅ Traffic Configuration: {'ps-preprod-ca-tstar': 100}
2025-05-30 12:25:09.869 | INFO     | __main__:main:1010 - ✅ Instance Type: STANDARD_NC4AS_T4_V3
2025-05-30 12:25:09.869 | INFO     | __main__:main:1011 - ✅ Instance Count: 1
2025-05-30 12:25:09.869 | INFO     | __main__:main:1012 - ✅ Workspace: ps-preprod-ml-claimsauto
2025-05-30 12:25:09.869 | INFO     | __main__:main:1013 - ✅ Resource Group: ps-preprod-rg-claimsauto
2025-05-30 12:25:09.869 | INFO     | __main__:main:1014 - ============================================================
2025-05-30 12:25:09.869 | INFO     | __main__:main:1017 - 📋 NEXT STEPS:
2025-05-30 12:25:09.869 | INFO     | __main__:main:1018 -    1. Test the deployed endpoint with sample data
2025-05-30 12:25:09.869 | INFO     | __main__:main:1019 -    2. Monitor deployment performance and logs
2025-05-30 12:25:09.869 | INFO     | __main__:main:1020 -    3. Update traffic allocation if needed
2025-05-30 12:25:09.869 | INFO     | __main__:main:1021 -    4. Set up monitoring and alerting
2025-05-30 12:25:09.869 | INFO     | __main__:main:1022 - ============================================================
2025-05-30 12:25:09.869 | SUCCESS  | __main__:main:1024 - 🎉 Deployment completed successfully!
2025-05-30 12:25:09.870 | INFO     | __main__:deployment_context:957 - Deployment context completed successfully
2025-05-30 12:25:09.870 | INFO     | __main__:deployment_context:962 - Deployment context cleanup completed
