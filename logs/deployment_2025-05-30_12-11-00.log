2025-05-30 12:11:00.663 | INFO     | __main__:setup_logging:98 - Logging configured - Log file: logs/deployment_2025-05-30_12-11-00.log
2025-05-30 12:11:00.664 | INFO     | __main__:deployment_context:954 - Starting deployment context
2025-05-30 12:11:00.664 | INFO     | __main__:main:974 - 🚀 AZURE ML DEPLOYMENT SCRIPT STARTED
2025-05-30 12:11:00.664 | INFO     | __main__:main:975 - ============================================================
2025-05-30 12:11:00.664 | INFO     | __main__:main:978 - 📋 STEP 1: Loading and validating configuration...
2025-05-30 12:11:00.666 | INFO     | __main__:load_and_validate_environment:942 - Environment configuration loaded and validated successfully
2025-05-30 12:11:00.666 | INFO     | __main__:main:980 - ✅ Configuration loaded successfully
2025-05-30 12:11:00.666 | INFO     | __main__:main:983 - 📊 STEP 2: Displaying deployment summary...
2025-05-30 12:11:00.667 | INFO     | __main__:display_deployment_summary:176 - ================================================================================
2025-05-30 12:11:00.667 | INFO     | __main__:display_deployment_summary:177 - DEPLOYMENT CONFIGURATION SUMMARY
2025-05-30 12:11:00.667 | INFO     | __main__:display_deployment_summary:178 - ================================================================================
2025-05-30 12:11:00.667 | INFO     | __main__:display_deployment_summary:181 - 🔧 Azure Environment:
2025-05-30 12:11:00.667 | INFO     | __main__:display_deployment_summary:182 -    Subscription ID: b15ae5d0-8f07-4cfb-aca3-508d38e9d983
2025-05-30 12:11:00.667 | INFO     | __main__:display_deployment_summary:183 -    Resource Group:  ps-preprod-rg-claimsauto
2025-05-30 12:11:00.667 | INFO     | __main__:display_deployment_summary:184 -    Workspace:       ps-preprod-ml-claimsauto
2025-05-30 12:11:00.667 | INFO     | __main__:display_deployment_summary:187 - 🚀 Deployment Configuration:
2025-05-30 12:11:00.667 | INFO     | __main__:display_deployment_summary:188 -    Model Name:      ps-preprod-ca-tstar
2025-05-30 12:11:00.667 | INFO     | __main__:display_deployment_summary:189 -    Environment:     ps-preprod-ca-tstar
2025-05-30 12:11:00.667 | INFO     | __main__:display_deployment_summary:190 -    Endpoint Name:   ps-preprod-ca-tstar
2025-05-30 12:11:00.667 | INFO     | __main__:display_deployment_summary:191 -    Deployment Name: ps-preprod-ca-tstar
2025-05-30 12:11:00.667 | INFO     | __main__:display_deployment_summary:194 - 💻 Infrastructure:
2025-05-30 12:11:00.667 | INFO     | __main__:display_deployment_summary:195 -    Instance Type:   Standard_D3_v2
2025-05-30 12:11:00.667 | INFO     | __main__:display_deployment_summary:196 -    Instance Count:  1
2025-05-30 12:11:00.668 | INFO     | __main__:display_deployment_summary:197 -    Timeout:         1800 seconds
2025-05-30 12:11:00.668 | INFO     | __main__:display_deployment_summary:198 -    Max Retries:     1
2025-05-30 12:11:00.668 | INFO     | __main__:display_deployment_summary:201 - 📁 File Validation:
2025-05-30 12:11:00.668 | INFO     | __main__:display_deployment_summary:207 -    Model File:      ✅ EXISTS (60.4 MB)
2025-05-30 12:11:00.668 | INFO     | __main__:display_deployment_summary:208 -    Model Path:      /home/<USER>/repos/autolodge_retrained_deploy/data/models/autolodge.h5
2025-05-30 12:11:00.668 | INFO     | __main__:display_deployment_summary:213 -    Conda File:      ✅ EXISTS
2025-05-30 12:11:00.668 | INFO     | __main__:display_deployment_summary:214 -    Conda Path:      /home/<USER>/repos/autolodge_retrained_deploy/configs/autolodge.yaml
2025-05-30 12:11:00.668 | INFO     | __main__:display_deployment_summary:219 -    Scoring Script:  ✅ EXISTS
2025-05-30 12:11:00.668 | INFO     | __main__:display_deployment_summary:220 -    Script Path:     /home/<USER>/repos/autolodge_retrained_deploy/Python/score.py
2025-05-30 12:11:00.668 | INFO     | __main__:display_deployment_summary:223 - 🔐 Environment Variables:
2025-05-30 12:11:00.668 | INFO     | __main__:display_deployment_summary:237 -    AZURE_SUBSCRIPTION_ID: ✅ b15ae5d0...
2025-05-30 12:11:00.668 | INFO     | __main__:display_deployment_summary:237 -    AZURE_RESOURCE_GROUP: ✅ ps-preprod-rg-claimsauto
2025-05-30 12:11:00.669 | INFO     | __main__:display_deployment_summary:237 -    AZURE_ML_WORKSPACE_NAME: ✅ ps-preprod-ml-claimsauto
2025-05-30 12:11:00.669 | INFO     | __main__:display_deployment_summary:237 -    MODEL_NAME: ✅ ps-preprod-ca-tstar
2025-05-30 12:11:00.669 | INFO     | __main__:display_deployment_summary:237 -    ENV_NAME: ✅ ps-preprod-ca-tstar
2025-05-30 12:11:00.669 | INFO     | __main__:display_deployment_summary:237 -    ENDPOINT_NAME: ✅ ps-preprod-ca-tstar
2025-05-30 12:11:00.669 | INFO     | __main__:display_deployment_summary:237 -    DEPLOYMENT_NAME: ✅ ps-preprod-ca-tstar
2025-05-30 12:11:00.669 | INFO     | __main__:display_deployment_summary:239 - ================================================================================
2025-05-30 12:11:00.669 | INFO     | __main__:main:987 - 🔐 STEP 3: User confirmation required...
2025-05-30 12:11:00.669 | INFO     | __main__:get_user_confirmation:252 - ⚠️  DEPLOYMENT CONFIRMATION REQUIRED
2025-05-30 12:11:00.669 | INFO     | __main__:get_user_confirmation:253 - ==================================================
2025-05-30 12:11:00.669 | WARNING  | __main__:get_user_confirmation:256 - 🎯 Target Environment: ps-preprod-ml-claimsauto
2025-05-30 12:11:00.669 | WARNING  | __main__:get_user_confirmation:257 - 🚀 Deployment Name: ps-preprod-ca-tstar
2025-05-30 12:11:00.669 | WARNING  | __main__:get_user_confirmation:258 - 📦 Model: ps-preprod-ca-tstar
2025-05-30 12:11:00.669 | WARNING  | __main__:get_user_confirmation:259 - 💰 Instance Type: Standard_D3_v2 (Count: 1)
2025-05-30 12:11:00.669 | INFO     | __main__:get_user_confirmation:262 - ⚠️  Potential Risks:
2025-05-30 12:11:00.669 | INFO     | __main__:get_user_confirmation:263 -    • This will create/update Azure ML resources
2025-05-30 12:11:00.670 | INFO     | __main__:get_user_confirmation:264 -    • Existing deployments with the same name may be overwritten
2025-05-30 12:11:00.670 | INFO     | __main__:get_user_confirmation:265 -    • Azure costs will be incurred for compute resources
2025-05-30 12:11:00.670 | INFO     | __main__:get_user_confirmation:266 -    • The deployment process may take 15-30 minutes
2025-05-30 12:11:00.670 | INFO     | __main__:get_user_confirmation:268 - ==================================================
2025-05-30 12:11:03.169 | INFO     | __main__:get_user_confirmation:277 - ✅ User confirmed deployment. Proceeding...
2025-05-30 12:11:03.169 | INFO     | __main__:main:993 - 🔧 STEP 4: Initializing Azure ML deployer...
2025-05-30 12:11:03.169 | INFO     | __main__:__init__:310 - Initialized AzureMLDeployer with configuration
2025-05-30 12:11:03.169 | INFO     | __main__:main:995 - ✅ Deployer initialized successfully
2025-05-30 12:11:03.169 | INFO     | __main__:main:998 - 🚀 STEP 5: Executing deployment...
2025-05-30 12:11:03.169 | INFO     | __main__:main:999 - ⚠️  WARNING: Irreversible deployment operations will now begin!
2025-05-30 12:11:03.169 | INFO     | __main__:deploy_with_retry:846 - 🔄 DEPLOYMENT EXECUTION PHASE
2025-05-30 12:11:03.169 | INFO     | __main__:deploy_with_retry:847 - ==================================================
2025-05-30 12:11:03.170 | INFO     | __main__:deploy_with_retry:848 - Starting deployment attempt 1/1
2025-05-30 12:11:03.170 | INFO     | __main__:deploy_with_retry:854 - 📦 STEP 1/5: Model Registration
2025-05-30 12:11:03.170 | INFO     | __main__:register_model:428 - 🔍 MODEL REGISTRATION PHASE
2025-05-30 12:11:03.170 | INFO     | __main__:register_model:429 - ========================================
2025-05-30 12:11:03.170 | INFO     | __main__:register_model:430 - Checking for existing model: ps-preprod-ca-tstar
2025-05-30 12:11:03.170 | INFO     | __main__:_create_ml_client:330 - Creating Azure ML client connection
2025-05-30 12:11:03.817 | INFO     | __main__:_create_ml_client:345 - Successfully connected to workspace: ps-preprod-ml-claimsauto
2025-05-30 12:11:04.512 | INFO     | __main__:register_model:435 - ✅ Found existing model: ps-preprod-ca-tstar (version: 1)
2025-05-30 12:11:04.513 | INFO     | __main__:register_model:436 -    Model ID: /subscriptions/b15ae5d0-8f07-4cfb-aca3-508d38e9d983/resourceGroups/ps-preprod-rg-claimsauto/providers/Microsoft.MachineLearningServices/workspaces/ps-preprod-ml-claimsauto/models/ps-preprod-ca-tstar/versions/1
2025-05-30 12:11:04.513 | INFO     | __main__:register_model:471 -    Created: 2025-05-30 11:49:52 AEST
2025-05-30 12:11:04.513 | INFO     | __main__:register_model:475 -    Description: Model registered on 2025-05-30T11:49:48.024090+10:00
2025-05-30 12:11:04.513 | INFO     | __main__:deploy_with_retry:858 - 🌍 STEP 2/5: Environment Setup
2025-05-30 12:11:04.514 | INFO     | __main__:create_environment:532 - 🌍 ENVIRONMENT SETUP PHASE
2025-05-30 12:11:04.514 | INFO     | __main__:create_environment:533 - ========================================
2025-05-30 12:11:04.514 | INFO     | __main__:create_environment:534 - Checking for existing environment: ps-preprod-ca-tstar
2025-05-30 12:11:05.393 | INFO     | __main__:create_environment:539 - ✅ Found existing environment: ps-preprod-ca-tstar (version: 1)
2025-05-30 12:11:05.394 | INFO     | __main__:create_environment:540 -    Environment ID: /subscriptions/b15ae5d0-8f07-4cfb-aca3-508d38e9d983/resourceGroups/ps-preprod-rg-claimsauto/providers/Microsoft.MachineLearningServices/workspaces/ps-preprod-ml-claimsauto/environments/ps-preprod-ca-tstar/versions/1
2025-05-30 12:11:05.394 | INFO     | __main__:create_environment:541 -    Base Image: mcr.microsoft.com/azureml/openmpi3.1.2-ubuntu18.04
2025-05-30 12:11:05.394 | INFO     | __main__:create_environment:542 -    Description: Environment created on 2025-05-30T11:49:54.212292+10:00
2025-05-30 12:11:05.394 | INFO     | __main__:deploy_with_retry:862 - 🌐 STEP 3/5: Endpoint Creation
2025-05-30 12:11:05.394 | INFO     | __main__:create_endpoint:613 - Checking for existing endpoint: ps-preprod-ca-tstar
2025-05-30 12:11:05.821 | INFO     | __main__:create_endpoint:618 - Found existing endpoint: ps-preprod-ca-tstar
2025-05-30 12:11:05.821 | INFO     | __main__:deploy_with_retry:866 - 🚀 STEP 4/5: Deployment Creation
2025-05-30 12:11:05.821 | INFO     | __main__:create_deployment:689 - 🚀 DEPLOYMENT CREATION PHASE
2025-05-30 12:11:05.821 | INFO     | __main__:create_deployment:690 - ========================================
2025-05-30 12:11:05.822 | INFO     | __main__:create_deployment:696 - 📁 Validating deployment files:
2025-05-30 12:11:05.822 | INFO     | __main__:create_deployment:697 -    Scoring script: /home/<USER>/repos/autolodge_retrained_deploy/Python/score.py
2025-05-30 12:11:05.822 | INFO     | __main__:create_deployment:698 -    Code directory: /home/<USER>/repos/autolodge_retrained_deploy/Python
2025-05-30 12:11:05.822 | INFO     | __main__:create_deployment:705 - ✅ Scoring script validation successful:
2025-05-30 12:11:05.822 | INFO     | __main__:create_deployment:706 -    File size: 34.88 KB
2025-05-30 12:11:05.822 | INFO     | __main__:create_deployment:715 -    score.py: ✅ EXISTS
2025-05-30 12:11:05.822 | INFO     | __main__:create_deployment:720 -    resources/: ✅ EXISTS
2025-05-30 12:11:05.822 | INFO     | __main__:create_deployment:720 -    logs/: ✅ EXISTS
2025-05-30 12:11:05.822 | INFO     | __main__:create_deployment:722 - 🔧 Deployment configuration:
2025-05-30 12:11:05.822 | INFO     | __main__:create_deployment:723 -    Deployment name: ps-preprod-ca-tstar
2025-05-30 12:11:05.822 | INFO     | __main__:create_deployment:724 -    Endpoint name: ps-preprod-ca-tstar
2025-05-30 12:11:05.823 | INFO     | __main__:create_deployment:725 -    Model: ps-preprod-ca-tstar (v1)
2025-05-30 12:11:05.823 | INFO     | __main__:create_deployment:726 -    Environment: ps-preprod-ca-tstar (v1)
2025-05-30 12:11:05.823 | INFO     | __main__:create_deployment:727 -    Instance type: Standard_D3_v2
2025-05-30 12:11:05.823 | INFO     | __main__:create_deployment:728 -    Instance count: 1
2025-05-30 12:11:05.823 | INFO     | __main__:create_deployment:741 - 🏷️  Environment variables:
2025-05-30 12:11:05.823 | INFO     | __main__:create_deployment:743 -    PYTHONPATH: /var/azureml-app/autolodge_retrained_deploy
2025-05-30 12:11:05.823 | INFO     | __main__:create_deployment:745 - 🏷️  Deployment tags:
2025-05-30 12:11:05.823 | INFO     | __main__:create_deployment:747 -    DeploymentDate: 2025-05-30T12:11:05.823266+10:00
2025-05-30 12:11:05.823 | INFO     | __main__:create_deployment:747 -    ModelName: ps-preprod-ca-tstar
2025-05-30 12:11:05.823 | INFO     | __main__:create_deployment:747 -    ModelVersion: 1
2025-05-30 12:11:05.823 | INFO     | __main__:create_deployment:747 -    EnvironmentName: ps-preprod-ca-tstar
2025-05-30 12:11:05.823 | INFO     | __main__:create_deployment:747 -    EnvironmentVersion: 1
2025-05-30 12:11:05.823 | INFO     | __main__:create_deployment:747 -    CreatedBy: AzureMLDeployer
2025-05-30 12:11:05.824 | INFO     | __main__:create_deployment:762 - 🚀 Starting deployment creation...
2025-05-30 12:11:05.824 | WARNING  | __main__:create_deployment:763 - ⏱️  This operation may take 15-30 minutes to complete
2025-05-30 12:11:09.609 | ERROR    | __main__:create_deployment:792 - ❌ Failed to create deployment ps-preprod-ca-tstar: (BadRequest) The request is invalid.
Code: BadRequest
Message: The request is invalid.
Exception Details:	(InferencingClientCallFailed) {"error":{"code":"Validation","message":"{\"errors\":{\"VmSize\":[\"The specified SKU 'Standard_D3_v2' is not supported. More details at https://docs.microsoft.com/en-us/azure/machine-learning/reference-managed-online-endpoints-vm-sku-list\"]},\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"traceId\":\"00-5c74abc6d81deb2474e417f572d1a44f-1f1bef46379e7952-01\"}"}}
	Code: InferencingClientCallFailed
	Message: {"error":{"code":"Validation","message":"{\"errors\":{\"VmSize\":[\"The specified SKU 'Standard_D3_v2' is not supported. More details at https://docs.microsoft.com/en-us/azure/machine-learning/reference-managed-online-endpoints-vm-sku-list\"]},\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"traceId\":\"00-5c74abc6d81deb2474e417f572d1a44f-1f1bef46379e7952-01\"}"}}
Additional Information:Type: ComponentName
Info: {
    "value": "managementfrontend"
}Type: Correlation
Info: {
    "value": {
        "operation": "5c74abc6d81deb2474e417f572d1a44f",
        "request": "6d7dc2961af37835"
    }
}Type: Environment
Info: {
    "value": "australiaeast"
}Type: Location
Info: {
    "value": "australiaeast"
}Type: Time
Info: {
    "value": "2025-05-30T02:11:09.5667615+00:00"
}
2025-05-30 12:11:09.609 | ERROR    | __main__:deploy_with_retry:879 - ❌ Deployment attempt 1 failed: Failed to create deployment ps-preprod-ca-tstar: (BadRequest) The request is invalid.
Code: BadRequest
Message: The request is invalid.
Exception Details:	(InferencingClientCallFailed) {"error":{"code":"Validation","message":"{\"errors\":{\"VmSize\":[\"The specified SKU 'Standard_D3_v2' is not supported. More details at https://docs.microsoft.com/en-us/azure/machine-learning/reference-managed-online-endpoints-vm-sku-list\"]},\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"traceId\":\"00-5c74abc6d81deb2474e417f572d1a44f-1f1bef46379e7952-01\"}"}}
	Code: InferencingClientCallFailed
	Message: {"error":{"code":"Validation","message":"{\"errors\":{\"VmSize\":[\"The specified SKU 'Standard_D3_v2' is not supported. More details at https://docs.microsoft.com/en-us/azure/machine-learning/reference-managed-online-endpoints-vm-sku-list\"]},\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"traceId\":\"00-5c74abc6d81deb2474e417f572d1a44f-1f1bef46379e7952-01\"}"}}
Additional Information:Type: ComponentName
Info: {
    "value": "managementfrontend"
}Type: Correlation
Info: {
    "value": {
        "operation": "5c74abc6d81deb2474e417f572d1a44f",
        "request": "6d7dc2961af37835"
    }
}Type: Environment
Info: {
    "value": "australiaeast"
}Type: Location
Info: {
    "value": "australiaeast"
}Type: Time
Info: {
    "value": "2025-05-30T02:11:09.5667615+00:00"
}
2025-05-30 12:11:09.609 | ERROR    | __main__:deploy_with_retry:886 - 💥 All deployment attempts failed
2025-05-30 12:11:09.609 | ERROR    | __main__:deploy_with_retry:893 - 💥 Deployment failed after 1 attempts. Last error: Failed to create deployment ps-preprod-ca-tstar: (BadRequest) The request is invalid.
Code: BadRequest
Message: The request is invalid.
Exception Details:	(InferencingClientCallFailed) {"error":{"code":"Validation","message":"{\"errors\":{\"VmSize\":[\"The specified SKU 'Standard_D3_v2' is not supported. More details at https://docs.microsoft.com/en-us/azure/machine-learning/reference-managed-online-endpoints-vm-sku-list\"]},\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"traceId\":\"00-5c74abc6d81deb2474e417f572d1a44f-1f1bef46379e7952-01\"}"}}
	Code: InferencingClientCallFailed
	Message: {"error":{"code":"Validation","message":"{\"errors\":{\"VmSize\":[\"The specified SKU 'Standard_D3_v2' is not supported. More details at https://docs.microsoft.com/en-us/azure/machine-learning/reference-managed-online-endpoints-vm-sku-list\"]},\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"traceId\":\"00-5c74abc6d81deb2474e417f572d1a44f-1f1bef46379e7952-01\"}"}}
Additional Information:Type: ComponentName
Info: {
    "value": "managementfrontend"
}Type: Correlation
Info: {
    "value": {
        "operation": "5c74abc6d81deb2474e417f572d1a44f",
        "request": "6d7dc2961af37835"
    }
}Type: Environment
Info: {
    "value": "australiaeast"
}Type: Location
Info: {
    "value": "australiaeast"
}Type: Time
Info: {
    "value": "2025-05-30T02:11:09.5667615+00:00"
}
2025-05-30 12:11:09.610 | ERROR    | __main__:deployment_context:959 - Deployment context failed: Deployment failed after 1 attempts. Last error: Failed to create deployment ps-preprod-ca-tstar: (BadRequest) The request is invalid.
Code: BadRequest
Message: The request is invalid.
Exception Details:	(InferencingClientCallFailed) {"error":{"code":"Validation","message":"{\"errors\":{\"VmSize\":[\"The specified SKU 'Standard_D3_v2' is not supported. More details at https://docs.microsoft.com/en-us/azure/machine-learning/reference-managed-online-endpoints-vm-sku-list\"]},\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"traceId\":\"00-5c74abc6d81deb2474e417f572d1a44f-1f1bef46379e7952-01\"}"}}
	Code: InferencingClientCallFailed
	Message: {"error":{"code":"Validation","message":"{\"errors\":{\"VmSize\":[\"The specified SKU 'Standard_D3_v2' is not supported. More details at https://docs.microsoft.com/en-us/azure/machine-learning/reference-managed-online-endpoints-vm-sku-list\"]},\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"traceId\":\"00-5c74abc6d81deb2474e417f572d1a44f-1f1bef46379e7952-01\"}"}}
Additional Information:Type: ComponentName
Info: {
    "value": "managementfrontend"
}Type: Correlation
Info: {
    "value": {
        "operation": "5c74abc6d81deb2474e417f572d1a44f",
        "request": "6d7dc2961af37835"
    }
}Type: Environment
Info: {
    "value": "australiaeast"
}Type: Location
Info: {
    "value": "australiaeast"
}Type: Time
Info: {
    "value": "2025-05-30T02:11:09.5667615+00:00"
}
2025-05-30 12:11:09.610 | INFO     | __main__:deployment_context:962 - Deployment context cleanup completed
2025-05-30 12:11:09.610 | ERROR    | __main__:main:1027 - ❌ Deployment failed: Deployment failed after 1 attempts. Last error: Failed to create deployment ps-preprod-ca-tstar: (BadRequest) The request is invalid.
Code: BadRequest
Message: The request is invalid.
Exception Details:	(InferencingClientCallFailed) {"error":{"code":"Validation","message":"{\"errors\":{\"VmSize\":[\"The specified SKU 'Standard_D3_v2' is not supported. More details at https://docs.microsoft.com/en-us/azure/machine-learning/reference-managed-online-endpoints-vm-sku-list\"]},\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"traceId\":\"00-5c74abc6d81deb2474e417f572d1a44f-1f1bef46379e7952-01\"}"}}
	Code: InferencingClientCallFailed
	Message: {"error":{"code":"Validation","message":"{\"errors\":{\"VmSize\":[\"The specified SKU 'Standard_D3_v2' is not supported. More details at https://docs.microsoft.com/en-us/azure/machine-learning/reference-managed-online-endpoints-vm-sku-list\"]},\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"traceId\":\"00-5c74abc6d81deb2474e417f572d1a44f-1f1bef46379e7952-01\"}"}}
Additional Information:Type: ComponentName
Info: {
    "value": "managementfrontend"
}Type: Correlation
Info: {
    "value": {
        "operation": "5c74abc6d81deb2474e417f572d1a44f",
        "request": "6d7dc2961af37835"
    }
}Type: Environment
Info: {
    "value": "australiaeast"
}Type: Location
Info: {
    "value": "australiaeast"
}Type: Time
Info: {
    "value": "2025-05-30T02:11:09.5667615+00:00"
}
2025-05-30 12:11:09.610 | ERROR    | __main__:main:1028 - 💡 Check the log file for detailed error information
