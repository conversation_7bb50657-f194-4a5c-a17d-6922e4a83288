2025-05-31 22:12:29.702 | INFO     | __main__:setup_logging:98 - Logging configured - Log file: logs/deployment_2025-05-31_22-12-29.log
2025-05-31 22:12:29.703 | INFO     | __main__:deployment_context:955 - Starting deployment context
2025-05-31 22:12:29.703 | INFO     | __main__:main:975 - 🚀 AZURE ML DEPLOYMENT SCRIPT STARTED
2025-05-31 22:12:29.703 | INFO     | __main__:main:976 - ============================================================
2025-05-31 22:12:29.703 | INFO     | __main__:main:979 - 📋 STEP 1: Loading and validating configuration...
2025-05-31 22:12:29.705 | INFO     | __main__:load_and_validate_environment:943 - Environment configuration loaded and validated successfully
2025-05-31 22:12:29.706 | INFO     | __main__:main:981 - ✅ Configuration loaded successfully
2025-05-31 22:12:29.706 | INFO     | __main__:main:984 - 📊 STEP 2: Displaying deployment summary...
2025-05-31 22:12:29.706 | INFO     | __main__:display_deployment_summary:177 - ================================================================================
2025-05-31 22:12:29.706 | INFO     | __main__:display_deployment_summary:178 - DEPLOYMENT CONFIGURATION SUMMARY
2025-05-31 22:12:29.706 | INFO     | __main__:display_deployment_summary:179 - ================================================================================
2025-05-31 22:12:29.706 | INFO     | __main__:display_deployment_summary:182 - 🔧 Azure Environment:
2025-05-31 22:12:29.706 | INFO     | __main__:display_deployment_summary:183 -    Subscription ID: b15ae5d0-8f07-4cfb-aca3-508d38e9d983
2025-05-31 22:12:29.706 | INFO     | __main__:display_deployment_summary:184 -    Resource Group:  t-to-tstar-rg
2025-05-31 22:12:29.706 | INFO     | __main__:display_deployment_summary:185 -    Workspace:       t-to-tstar
2025-05-31 22:12:29.706 | INFO     | __main__:display_deployment_summary:188 - 🚀 Deployment Configuration:
2025-05-31 22:12:29.706 | INFO     | __main__:display_deployment_summary:189 -    Model Name:      ps-test-ca-tstarc
2025-05-31 22:12:29.706 | INFO     | __main__:display_deployment_summary:190 -    Environment:     ps-test-ca-tstarc
2025-05-31 22:12:29.706 | INFO     | __main__:display_deployment_summary:191 -    Endpoint Name:   ps-test-ca-tstarc
2025-05-31 22:12:29.706 | INFO     | __main__:display_deployment_summary:192 -    Deployment Name: ps-test-ca-tstarc
2025-05-31 22:12:29.707 | INFO     | __main__:display_deployment_summary:195 - 💻 Infrastructure:
2025-05-31 22:12:29.707 | INFO     | __main__:display_deployment_summary:196 -    Instance Type:   STANDARD_DS3_V2
2025-05-31 22:12:29.707 | INFO     | __main__:display_deployment_summary:197 -    Instance Count:  1
2025-05-31 22:12:29.707 | INFO     | __main__:display_deployment_summary:198 -    Timeout:         1800 seconds
2025-05-31 22:12:29.707 | INFO     | __main__:display_deployment_summary:199 -    Max Retries:     1
2025-05-31 22:12:29.707 | INFO     | __main__:display_deployment_summary:202 - 📁 File Validation:
2025-05-31 22:12:29.707 | INFO     | __main__:display_deployment_summary:208 -    Model File:      ✅ EXISTS (60.4 MB)
2025-05-31 22:12:29.707 | INFO     | __main__:display_deployment_summary:209 -    Model Path:      /home/<USER>/repos/autolodge_retrained_deploy/Python/resources/autolodge.h5
2025-05-31 22:12:29.707 | INFO     | __main__:display_deployment_summary:214 -    Conda File:      ✅ EXISTS
2025-05-31 22:12:29.707 | INFO     | __main__:display_deployment_summary:215 -    Conda Path:      /home/<USER>/repos/autolodge_retrained_deploy/configs/autolodge.yaml
2025-05-31 22:12:29.707 | INFO     | __main__:display_deployment_summary:220 -    Scoring Script:  ✅ EXISTS
2025-05-31 22:12:29.707 | INFO     | __main__:display_deployment_summary:221 -    Script Path:     /home/<USER>/repos/autolodge_retrained_deploy/Python/score.py
2025-05-31 22:12:29.708 | INFO     | __main__:display_deployment_summary:224 - 🔐 Environment Variables:
2025-05-31 22:12:29.708 | INFO     | __main__:display_deployment_summary:238 -    AZURE_SUBSCRIPTION_ID: ✅ b15ae5d0...
2025-05-31 22:12:29.708 | INFO     | __main__:display_deployment_summary:238 -    AZURE_RESOURCE_GROUP: ✅ t-to-tstar-rg
2025-05-31 22:12:29.708 | INFO     | __main__:display_deployment_summary:238 -    AZURE_ML_WORKSPACE_NAME: ✅ t-to-tstar
2025-05-31 22:12:29.708 | INFO     | __main__:display_deployment_summary:238 -    MODEL_NAME: ✅ ps-test-ca-tstarc
2025-05-31 22:12:29.708 | INFO     | __main__:display_deployment_summary:238 -    ENV_NAME: ✅ ps-test-ca-tstarc
2025-05-31 22:12:29.708 | INFO     | __main__:display_deployment_summary:238 -    ENDPOINT_NAME: ✅ ps-test-ca-tstarc
2025-05-31 22:12:29.708 | INFO     | __main__:display_deployment_summary:238 -    DEPLOYMENT_NAME: ✅ ps-test-ca-tstarc
2025-05-31 22:12:29.708 | INFO     | __main__:display_deployment_summary:240 - ================================================================================
2025-05-31 22:12:29.708 | INFO     | __main__:main:988 - 🔐 STEP 3: User confirmation required...
2025-05-31 22:12:29.708 | INFO     | __main__:get_user_confirmation:253 - ⚠️  DEPLOYMENT CONFIRMATION REQUIRED
2025-05-31 22:12:29.708 | INFO     | __main__:get_user_confirmation:254 - ==================================================
2025-05-31 22:12:29.708 | WARNING  | __main__:get_user_confirmation:257 - 🎯 Target Environment: t-to-tstar
2025-05-31 22:12:29.708 | WARNING  | __main__:get_user_confirmation:258 - 🚀 Deployment Name: ps-test-ca-tstarc
2025-05-31 22:12:29.708 | WARNING  | __main__:get_user_confirmation:259 - 📦 Model: ps-test-ca-tstarc
2025-05-31 22:12:29.709 | WARNING  | __main__:get_user_confirmation:260 - 💰 Instance Type: STANDARD_DS3_V2 (Count: 1)
2025-05-31 22:12:29.709 | INFO     | __main__:get_user_confirmation:263 - ⚠️  Potential Risks:
2025-05-31 22:12:29.709 | INFO     | __main__:get_user_confirmation:264 -    • This will create/update Azure ML resources
2025-05-31 22:12:29.709 | INFO     | __main__:get_user_confirmation:265 -    • Existing deployments with the same name may be overwritten
2025-05-31 22:12:29.709 | INFO     | __main__:get_user_confirmation:266 -    • Azure costs will be incurred for compute resources
2025-05-31 22:12:29.709 | INFO     | __main__:get_user_confirmation:267 -    • The deployment process may take 15-30 minutes
2025-05-31 22:12:29.709 | INFO     | __main__:get_user_confirmation:269 - ==================================================
2025-05-31 22:12:48.761 | WARNING  | __main__:get_user_confirmation:281 - ❌ User cancelled deployment.
2025-05-31 22:12:48.762 | WARNING  | __main__:main:990 - ❌ Deployment cancelled by user
2025-05-31 22:12:48.762 | INFO     | __main__:deployment_context:963 - Deployment context cleanup completed
