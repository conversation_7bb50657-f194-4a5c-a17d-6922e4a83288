2025-05-30 09:43:44.218 | INFO     | submit_deploy_v2_refactored:setup_logging:98 - Logging configured - Log file: logs/deployment_2025-05-30_09-43-44.log
2025-05-30 09:43:44.229 | INFO     | submit_deploy_v2_refactored:setup_logging:98 - Logging configured - Log file: logs/deployment_2025-05-30_09-43-44.log
2025-05-30 09:43:44.231 | INFO     | submit_deploy_v2_refactored:display_deployment_summary:176 - ================================================================================
2025-05-30 09:43:44.231 | INFO     | submit_deploy_v2_refactored:display_deployment_summary:177 - DEPLOYMENT CONFIGURATION SUMMARY
2025-05-30 09:43:44.231 | INFO     | submit_deploy_v2_refactored:display_deployment_summary:178 - ================================================================================
2025-05-30 09:43:44.231 | INFO     | submit_deploy_v2_refactored:display_deployment_summary:181 - 🔧 Azure Environment:
2025-05-30 09:43:44.231 | INFO     | submit_deploy_v2_refactored:display_deployment_summary:182 -    Subscription ID: test-subscription-id
2025-05-30 09:43:44.231 | INFO     | submit_deploy_v2_refactored:display_deployment_summary:183 -    Resource Group:  test-resource-group
2025-05-30 09:43:44.231 | INFO     | submit_deploy_v2_refactored:display_deployment_summary:184 -    Workspace:       test-workspace
2025-05-30 09:43:44.231 | INFO     | submit_deploy_v2_refactored:display_deployment_summary:187 - 🚀 Deployment Configuration:
2025-05-30 09:43:44.232 | INFO     | submit_deploy_v2_refactored:display_deployment_summary:188 -    Model Name:      test-model
2025-05-30 09:43:44.232 | INFO     | submit_deploy_v2_refactored:display_deployment_summary:189 -    Environment:     test-env
2025-05-30 09:43:44.232 | INFO     | submit_deploy_v2_refactored:display_deployment_summary:190 -    Endpoint Name:   test-endpoint
2025-05-30 09:43:44.232 | INFO     | submit_deploy_v2_refactored:display_deployment_summary:191 -    Deployment Name: test-deployment
2025-05-30 09:43:44.232 | INFO     | submit_deploy_v2_refactored:display_deployment_summary:194 - 💻 Infrastructure:
2025-05-30 09:43:44.232 | INFO     | submit_deploy_v2_refactored:display_deployment_summary:195 -    Instance Type:   Standard_DS3_v2
2025-05-30 09:43:44.232 | INFO     | submit_deploy_v2_refactored:display_deployment_summary:196 -    Instance Count:  1
2025-05-30 09:43:44.232 | INFO     | submit_deploy_v2_refactored:display_deployment_summary:197 -    Timeout:         1800 seconds
2025-05-30 09:43:44.232 | INFO     | submit_deploy_v2_refactored:display_deployment_summary:198 -    Max Retries:     1
2025-05-30 09:43:44.232 | INFO     | submit_deploy_v2_refactored:display_deployment_summary:201 - 📁 File Validation:
2025-05-30 09:43:44.232 | INFO     | submit_deploy_v2_refactored:display_deployment_summary:207 -    Model File:      ❌ MISSING 
2025-05-30 09:43:44.232 | INFO     | submit_deploy_v2_refactored:display_deployment_summary:208 -    Model Path:      /home/<USER>/repos/autolodge_retrained_deploy/resources/models/ps-dev-claimsauto-tstarc.h5
2025-05-30 09:43:44.232 | INFO     | submit_deploy_v2_refactored:display_deployment_summary:213 -    Conda File:      ✅ EXISTS
2025-05-30 09:43:44.233 | INFO     | submit_deploy_v2_refactored:display_deployment_summary:214 -    Conda Path:      /home/<USER>/repos/autolodge_retrained_deploy/configs/ps-dev-claimsauto-tstarc.yaml
2025-05-30 09:43:44.233 | INFO     | submit_deploy_v2_refactored:display_deployment_summary:219 -    Scoring Script:  ✅ EXISTS
2025-05-30 09:43:44.233 | INFO     | submit_deploy_v2_refactored:display_deployment_summary:220 -    Script Path:     /home/<USER>/repos/autolodge_retrained_deploy/Python/score.py
2025-05-30 09:43:44.233 | INFO     | submit_deploy_v2_refactored:display_deployment_summary:223 - 🔐 Environment Variables:
2025-05-30 09:43:44.233 | INFO     | submit_deploy_v2_refactored:display_deployment_summary:237 -    AZURE_SUBSCRIPTION_ID: ✅ test-sub...
2025-05-30 09:43:44.233 | INFO     | submit_deploy_v2_refactored:display_deployment_summary:237 -    AZURE_RESOURCE_GROUP: ✅ test-resource-group
2025-05-30 09:43:44.233 | INFO     | submit_deploy_v2_refactored:display_deployment_summary:237 -    AZURE_ML_WORKSPACE_NAME: ✅ test-workspace
2025-05-30 09:43:44.233 | INFO     | submit_deploy_v2_refactored:display_deployment_summary:237 -    MODEL_NAME: ✅ test-model
2025-05-30 09:43:44.233 | INFO     | submit_deploy_v2_refactored:display_deployment_summary:237 -    ENV_NAME: ✅ test-env
2025-05-30 09:43:44.233 | INFO     | submit_deploy_v2_refactored:display_deployment_summary:237 -    ENDPOINT_NAME: ✅ test-endpoint
2025-05-30 09:43:44.233 | INFO     | submit_deploy_v2_refactored:display_deployment_summary:237 -    DEPLOYMENT_NAME: ✅ test-deployment
2025-05-30 09:43:44.233 | INFO     | submit_deploy_v2_refactored:display_deployment_summary:239 - ================================================================================
2025-05-30 09:43:44.235 | INFO     | submit_deploy_v2_refactored:get_user_confirmation:252 - ⚠️  DEPLOYMENT CONFIRMATION REQUIRED
2025-05-30 09:43:44.235 | INFO     | submit_deploy_v2_refactored:get_user_confirmation:253 - ==================================================
2025-05-30 09:43:44.235 | WARNING  | submit_deploy_v2_refactored:get_user_confirmation:256 - 🎯 Target Environment: test-workspace
2025-05-30 09:43:44.235 | WARNING  | submit_deploy_v2_refactored:get_user_confirmation:257 - 🚀 Deployment Name: test-deployment
2025-05-30 09:43:44.235 | WARNING  | submit_deploy_v2_refactored:get_user_confirmation:258 - 📦 Model: test-model
2025-05-30 09:43:44.235 | WARNING  | submit_deploy_v2_refactored:get_user_confirmation:259 - 💰 Instance Type: Standard_DS3_v2 (Count: 1)
2025-05-30 09:43:44.235 | INFO     | submit_deploy_v2_refactored:get_user_confirmation:262 - ⚠️  Potential Risks:
2025-05-30 09:43:44.235 | INFO     | submit_deploy_v2_refactored:get_user_confirmation:263 -    • This will create/update Azure ML resources
2025-05-30 09:43:44.235 | INFO     | submit_deploy_v2_refactored:get_user_confirmation:264 -    • Existing deployments with the same name may be overwritten
2025-05-30 09:43:44.235 | INFO     | submit_deploy_v2_refactored:get_user_confirmation:265 -    • Azure costs will be incurred for compute resources
2025-05-30 09:43:44.235 | INFO     | submit_deploy_v2_refactored:get_user_confirmation:266 -    • The deployment process may take 15-30 minutes
2025-05-30 09:43:44.236 | INFO     | submit_deploy_v2_refactored:get_user_confirmation:268 - ==================================================
2025-05-30 09:43:44.236 | INFO     | submit_deploy_v2_refactored:get_user_confirmation:277 - ✅ User confirmed deployment. Proceeding...
2025-05-30 09:43:44.236 | INFO     | submit_deploy_v2_refactored:get_user_confirmation:252 - ⚠️  DEPLOYMENT CONFIRMATION REQUIRED
2025-05-30 09:43:44.236 | INFO     | submit_deploy_v2_refactored:get_user_confirmation:253 - ==================================================
2025-05-30 09:43:44.236 | WARNING  | submit_deploy_v2_refactored:get_user_confirmation:256 - 🎯 Target Environment: test-workspace
2025-05-30 09:43:44.236 | WARNING  | submit_deploy_v2_refactored:get_user_confirmation:257 - 🚀 Deployment Name: test-deployment
2025-05-30 09:43:44.236 | WARNING  | submit_deploy_v2_refactored:get_user_confirmation:258 - 📦 Model: test-model
2025-05-30 09:43:44.236 | WARNING  | submit_deploy_v2_refactored:get_user_confirmation:259 - 💰 Instance Type: Standard_DS3_v2 (Count: 1)
2025-05-30 09:43:44.236 | INFO     | submit_deploy_v2_refactored:get_user_confirmation:262 - ⚠️  Potential Risks:
2025-05-30 09:43:44.236 | INFO     | submit_deploy_v2_refactored:get_user_confirmation:263 -    • This will create/update Azure ML resources
2025-05-30 09:43:44.237 | INFO     | submit_deploy_v2_refactored:get_user_confirmation:264 -    • Existing deployments with the same name may be overwritten
2025-05-30 09:43:44.237 | INFO     | submit_deploy_v2_refactored:get_user_confirmation:265 -    • Azure costs will be incurred for compute resources
2025-05-30 09:43:44.237 | INFO     | submit_deploy_v2_refactored:get_user_confirmation:266 -    • The deployment process may take 15-30 minutes
2025-05-30 09:43:44.237 | INFO     | submit_deploy_v2_refactored:get_user_confirmation:268 - ==================================================
2025-05-30 09:43:44.237 | WARNING  | submit_deploy_v2_refactored:get_user_confirmation:280 - ❌ User cancelled deployment.
